#!/bin/bash
set -e

if [[ "$1" != --environmentId=* ]]; then
  echo "Usage: $0 --environmentId=<environmentId>"
  exit 1
fi

environmentId="${1#--environmentId=}"

if [ -z "$1" ]; then
  echo "Error: No environmentId provided."
  exit 1
fi

# Run database migrations, one_off tasks and asset sync
./bin/rails db:migrate one_off:run[false] assets:sync

response=$(curl -s -X GET "https://api.render.com/v1/services?environmentId=$environmentId&type=background_worker" \
  -H "Authorization: Bearer $RENDER_API_KEY" \
  -H "Accept: application/json")

service_ids=$(echo "$response" | jq -r '.[] | .service.id')

if [ -z "$service_ids" ]; then
  echo "No background worker services found for environmentId: $environmentId"
  exit 0
fi

for service_id in $service_ids; do
  echo "Triggering deployment for service ID: $service_id"
  curl -s -X POST "https://api.render.com/v1/services/$service_id/deploys" \
    -H "Authorization: Bearer $RENDER_API_KEY" \
    -H "Accept: application/json" \
    -o /dev/null
  if [ $? -ne 0 ]; then
    echo "Failed to trigger deployment for service ID: $service_id"
    exit 1
  fi
  echo "Deployment triggered successfully for service ID: $service_id"
done
