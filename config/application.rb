require_relative 'boot'

require 'rails'

require 'active_record/railtie'
require 'active_storage/engine'
require 'action_controller/railtie'
require 'action_view/railtie'
require 'action_mailer/railtie'
require 'active_job/railtie'
require 'action_cable/engine'
require 'action_text/engine'

Bundler.require(*Rails.groups)

module CarrierSource
  class Application < Rails::Application
    config.load_defaults 8.0

    config.asset_host = ENV.fetch('ASSET_HOST', nil)

    config.cache_store = :memory_store

    config.middleware.use Rack::CanonicalHost, ENV.fetch('CANONICAL_HOST', nil),
                          ignore: %w(api.carriersource.io staging-api.carriersource.io)
    config.middleware.use Rack::Deflater
    config.middleware.use Rack::Utm

    config.time_zone = 'Central Time (US & Canada)'

    config.generators do |g|
      g.test_framework :rspec
    end

    config.autoload_lib_once(ignore: %w(assets generators tasks))

    config.active_job.queue_adapter = :sidekiq

    config.active_record.index_nested_attribute_errors = true
    config.active_record.schema_format = :sql

    config.active_storage.variable_content_types << 'image/svg+xml'
    config.active_storage.resolve_model_to_route = :rails_storage_proxy

    routes.default_url_options = {
      host: ENV.fetch('DEFAULT_URL_HOST', 'www.carriersource.io'),
      port: ENV.fetch('DEFAULT_URL_PORT', nil),
      protocol: ENV.fetch('DEFAULT_URL_PROTOCOL', 'https')
    }

    config.action_mailer.deliver_later_queue_name = 'mailer'
    config.action_mailer.smtp_settings = {
      address: 'email-smtp.us-east-1.amazonaws.com',
      port: 587,
      authentication: 'login',
      domain: 'carriersource.io',
      enable_starttls_auto: true,
      user_name: ENV.fetch('SMTP_USERNAME', nil),
      password: ENV.fetch('SMTP_PASSWORD', nil)
    }
    config.action_mailer.asset_host = routes.default_url_options[:host]
    config.action_mailer.show_previews = true
    config.action_mailer.preview_paths << Rails.root.join('spec/mailers/previews').to_s

    config.middleware.insert 0, Rack::ReverseProxy do
      reverse_proxy_options preserve_host: true, timeout: 15

      reverse_proxy %r{^(/sitemaps/[\w-]+\.(xml|xml\.gz))$},
                    "https://#{CarrierSource::Credentials.lookup(:sitemap, :bucket)}.s3.amazonaws.com$1",
                    username: nil, password: nil
    end

    require 'cookie_consent'
    config.middleware.insert_before Clearance::RackSession, CookieConsent

    require 'unique_session_cookie'
    config.middleware.use UniqueSessionCookie

    require 'clearance/impersonation'
    config.middleware.use Clearance::Impersonation

    config.middleware.insert_before(
      ActionDispatch::Cookies, Rack::StripCookies,
      paths: %w(/rails/active_storage/blobs/proxy/* /rails/active_storage/representations/proxy/* /widgets/*)
    )
  end
end
