ActiveSupport.on_load('carrier_source/wisper') do
  Wisper.subscribe(Webhooks::ModelListener, scope: ApplicationRecord)
  Wisper.subscribe(Webhooks::DeliveryListener, scope: Webhooks::Event)
  Wisper.subscribe(Marketing::SyncCarrierListener, scope: ApplicationRecord)
  Wisper.subscribe(Marketing::SyncBrokerageListener, scope: ApplicationRecord)
  Wisper.subscribe(AccessPackages::Subscriptions::SyncListener, scope: Subscription)
  Wisper.subscribe(Users::LoginListener)
  Wisper.subscribe(Slack::RfpListener, scope: RequestForProposal)
  Wisper.subscribe(Companies::ClaimListener)
  Wisper.subscribe(Users::AnalyticsCompanyListener, scope: User)
end
