components = Rails.application.routes.default_url_options
default_host = Addressable::URI.new(scheme: components[:protocol], **components).to_s

SitemapGenerator::Sitemap.then do |config|
  config.default_host = default_host
  config.sitemaps_host = default_host
  config.sitemaps_path = 'sitemaps/'
  config.public_path = 'tmp/'
  config.create_index = true
  config.adapter = SitemapGenerator::AwsS3Adapter.new(CarrierSource::Credentials.lookup(:sitemap, :bucket))
end

SitemapGenerator::Sitemap.create do
  %i(freights truck_types shipment_types specialized_services).each do |type|
    # rubocop:disable Rails/FindEach
    Records[type].all.each do |record|
      unless Companies::FreightAssumptions.load_alias_mapping.key?(record.slug)
        add load_trucking_companies_path(record), changefreq: 'weekly', priority: 0.8
      end
    end
    # rubocop:enable Rails/FindEach
  end

  %w(terms-of-service privacy-policy cookie-policy content-and-data-usage).each do |page|
    add page_path(page), changefreq: 'monthly', priority: 0.1
  end

  add write_review_path, changefreq: 'monthly', priority: 0.1

  Carrier.carrier.indexable.authorized.merge(OperatingAuthority.active).includes(:reviews_aggregate, profile: :users)
    .distinct.find_each do |carrier|
    priority = Sitemaps::Carrier.new(carrier).priority
    add carrier_path(carrier), changefreq: 'weekly', priority:, lastmod: carrier.updated_at
  end

  add trucking_companies_path, priority: 0.1

  # rubocop:disable Rails/FindEach
  Geo::Country.all.each do |country|
    add country_trucking_companies_path(country.slug), priority: 0.1

    country.states.each do |state|
      add state_trucking_companies_path(*state.path), priority: 0.2
    end
  end
  # rubocop:enable Rails/FindEach

  CarrierCity.includes(:city).find_each do |tcc|
    add city_trucking_companies_path(*tcc.city.path), changefreq: 'weekly', priority: 0.7
  end
end
