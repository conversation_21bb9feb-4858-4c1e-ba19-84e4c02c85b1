services:
  elasticsearch:
    image: elasticsearch:9.1.2
    ports:
      - '9200:9200'
    volumes:
      - ./elasticsearch/config/elasticsearch.yml:/usr/share/elasticsearch/config/elasticsearch.yml
      - elasticsearch-data:/usr/share/elasticsearch/data
    environment:
      ES_JAVA_OPTS: '-Xms512m -Xmx512m'
  kibana:
    image: kibana:9.1.2
    ports:
      - '5601:5601'
    depends_on:
      - elasticsearch
    external_links:
      - elasticsearch
  postgis:
    image: postgis/postgis:17-3.5
    environment:
      POSTGRES_PASSWORD: postgres
    ports:
      - '5432:5432'
    shm_size: 1g
    volumes:
      - postgres-17-data:/var/lib/postgresql/data
  valkey:
    image: valkey/valkey:8.1.1
    ports:
      - '6379:6379'
volumes:
  elasticsearch-data:
  postgres-17-data:
