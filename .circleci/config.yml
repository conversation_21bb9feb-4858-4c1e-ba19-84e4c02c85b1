version: 2.1

orbs:
  codecov: codecov/codecov@3.2.3
  docker: circleci/docker@2.8.2
  node: circleci/node@6.1.0
  ruby: circleci/ruby@2.1.0

jobs:
  node_specs:
    docker:
      - image: cimg/node:22.11.0
    steps:
      - checkout
      - node/install-packages:
          include-branch-in-cache-key: false
          pkg-manager: yarn-berry
      - run:
          command: yarn test
          name: Node Tests
  ruby_specs:
    docker:
      - image: cimg/ruby:3.4.5-browsers
      - image: postgis/postgis:17-3.5
        environment:
          POSTGRES_PASSWORD: postgres
      - image: elasticsearch:9.1.2
        environment:
          - action.destructive_requires_name: false
          - discovery.type: single-node
          - xpack.security.enabled: false
      - image: valkey/valkey:8.1.0
    parallelism: 2
    environment:
      ELASTICSEARCH_URL: '**************************************'
      ES_JAVA_OPTS: '-Xms512m -Xmx1024m'
      PAGER: cat
      RACK_ENV: test
      RAILS_ENV: test
      RUBYOPT: -W:deprecated --enable-yjit
    steps:
      - checkout
      - node/install-packages:
          include-branch-in-cache-key: false
          pkg-manager: yarn-berry
      - ruby/install-deps:
          key: 'gems-v3'
          include-branch-in-cache-key: false
          clean-bundle: true
      - run:
          command: sudo apt-get update -qq && sudo apt-get install --no-install-recommends -y libvips42
          name: Install libvips
      - run:
          command: 'dockerize -wait tcp://localhost:5432 -wait http://localhost:9200 -timeout 1m'
          name: Wait for dependent services
      - run:
          command: 'bundle exec rails db:setup --trace'
          name: Database setup
      - run:
          command: yarn build
          name: Build Assets
      - run:
          command: bundle exec rails assets:precompile
          name: Precompile Assets
      - ruby/rspec-test
      - store_artifacts:
          path: tmp/capybara
      - codecov/upload
  deploy:
    machine: true
    steps:
      - run:
          name: Trigger Render Deployment
          command: |
            curl -X POST "https://api.render.com/v1/services/srv-coirkmv79t8c738m49k0/deploys" \
                 -H "Authorization: Bearer $RENDER_API_KEY"
workflows:
  main:
    jobs:
      - node_specs:
          name: Node Specs
          context: global
      - ruby_specs:
          name: Ruby Specs
          context: global
      - docker/publish:
          name: Publish Docker Image
          context:
            - global
            - docker
          executor: docker/docker
          cache_from: ghcr.io/carriersource-code/carrier_source:buildcache
          cache_to: ghcr.io/carriersource-code/carrier_source:buildcache
          image: carriersource-code/carrier_source
          tag: '${CIRCLE_SHA1},latest'
          extra_build_args: '--build-arg=BUNDLE_ENTERPRISE__CONTRIBSYS__COM=${BUNDLE_ENTERPRISE__CONTRIBSYS__COM}'
          registry: ghcr.io
          use-buildkit: true
          use-remote-docker: true
          requires:
            - Node Specs
            - Ruby Specs
          filters:
            branches:
              only: main
      - deploy:
          name: Deploy
          context: render
          requires:
            - Publish Docker Image
          filters:
            branches:
              only: main
