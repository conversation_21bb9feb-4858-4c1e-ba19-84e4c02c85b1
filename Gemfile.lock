GIT
  remote: https://github.com/hasghari/activeadmin.git
  revision: 648194a0a9757cc456db9d6ce8be8baa146b2f84
  branch: main-menu-navigation
  specs:
    activeadmin (4.0.0.beta16)
      arbre (~> 2.0)
      csv
      formtastic (>= 5.0)
      formtastic_i18n (>= 0.7)
      inherited_resources (~> 2.0)
      kaminari (>= 1.2.1)
      railties (>= 7.0)
      ransack (>= 4.0)

GIT
  remote: https://github.com/hasghari/ahoy.git
  revision: 5d10839c399700dd0401d1d1c065bc5a1c6e5afa
  branch: load-hooks
  specs:
    ahoy_matey (5.1.0)
      activesupport (>= 6.1)
      device_detector (>= 1)
      safely_block (>= 0.4)

GIT
  remote: https://github.com/hasghari/annotate_models.git
  revision: 0008823c0c057e158e2124306278cbea0a0cc0c7
  branch: rails-8
  specs:
    annotate (3.2.0)
      activerecord (>= 3.2, < 9.0)
      rake (>= 10.4, < 14.0)

GIT
  remote: https://github.com/samedi/rack-reverse-proxy.git
  revision: 06f21feb6afbbf902969c4f1df219df8f2080387
  branch: main
  specs:
    rack-reverse-proxy (1.0.0.pre.unreleased)
      rack (>= 1.0.0)
      rack-proxy (~> 0.7, >= 0.7.0)

GEM
  remote: https://enterprise.contribsys.com/
  specs:
    sidekiq-ent (8.0.2)
      einhorn (~> 1.0)
      get_process_mem
      gserver
      sidekiq (>= 8.0.3, < 9)
      sidekiq-pro (>= 8.0.0, < 9)
    sidekiq-pro (8.0.2)
      sidekiq (>= 8.0.0, < 9)

GEM
  remote: https://rubygems.org/
  specs:
    Ascii85 (2.0.1)
    aasm (5.5.1)
      concurrent-ruby (~> 1.0)
    actioncable (*******)
      actionpack (= *******)
      activesupport (= *******)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
      zeitwerk (~> 2.6)
    actionmailbox (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
    actionmailer (*******)
      actionpack (= *******)
      actionview (= *******)
      activejob (= *******)
      activesupport (= *******)
      mail (>= 2.8.0)
      rails-dom-testing (~> 2.2)
    actionpack (*******)
      actionview (= *******)
      activesupport (= *******)
      nokogiri (>= 1.8.5)
      rack (>= 2.2.4)
      rack-session (>= 1.0.1)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
      useragent (~> 0.16)
    actiontext (*******)
      actionpack (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (*******)
      activesupport (= *******)
      builder (~> 3.1)
      erubi (~> 1.11)
      rails-dom-testing (~> 2.2)
      rails-html-sanitizer (~> 1.6)
    activejob (*******)
      activesupport (= *******)
      globalid (>= 0.3.6)
    activemodel (*******)
      activesupport (= *******)
    activerecord (*******)
      activemodel (= *******)
      activesupport (= *******)
      timeout (>= 0.4.0)
    activestorage (*******)
      actionpack (= *******)
      activejob (= *******)
      activerecord (= *******)
      activesupport (= *******)
      marcel (~> 1.0)
    activesupport (*******)
      base64
      benchmark (>= 0.3)
      bigdecimal
      concurrent-ruby (~> 1.0, >= 1.3.1)
      connection_pool (>= 2.2.5)
      drb
      i18n (>= 1.6, < 2)
      logger (>= 1.4.2)
      minitest (>= 5.1)
      securerandom (>= 0.3)
      tzinfo (~> 2.0, >= 2.0.5)
      uri (>= 0.13.1)
    addressable (2.8.7)
      public_suffix (>= 2.0.2, < 7.0)
    afm (1.0.0)
    after_commit_everywhere (1.6.0)
      activerecord (>= 4.2)
      activesupport
    arbre (2.2.0)
      activesupport (>= 7.0)
    argon2 (2.3.2)
      ffi (~> 1.15)
      ffi-compiler (~> 1.0)
    asset_sync (2.19.2)
      activemodel (>= 4.1.0)
      fog-core
      mime-types (>= 2.99)
      unf
    ast (2.4.3)
    audited (5.8.0)
      activerecord (>= 5.2, < 8.2)
      activesupport (>= 5.2, < 8.2)
    aws-eventstream (1.4.0)
    aws-partitions (1.1151.0)
    aws-sdk-cloudwatch (1.120.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-core (3.231.0)
      aws-eventstream (~> 1, >= 1.3.0)
      aws-partitions (~> 1, >= 1.992.0)
      aws-sigv4 (~> 1.9)
      base64
      bigdecimal
      jmespath (~> 1, >= 1.6.1)
      logger
    aws-sdk-kms (1.111.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sigv4 (~> 1.5)
    aws-sdk-s3 (1.198.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sdk-kms (~> 1)
      aws-sigv4 (~> 1.5)
    aws-sdk-sesv2 (1.84.0)
      aws-sdk-core (~> 3, >= 3.231.0)
      aws-sigv4 (~> 1.5)
    aws-sigv4 (1.12.1)
      aws-eventstream (~> 1, >= 1.0.2)
    baran (0.2.1)
    base64 (0.3.0)
    bcrypt (3.1.20)
    benchmark (0.4.1)
    bigdecimal (3.2.2)
    blueprinter (1.1.2)
    bootsnap (1.18.6)
      msgpack (~> 1.2)
    browser (6.2.0)
    builder (3.3.0)
    bulk_insert (1.9.0)
      activerecord (>= 3.2.0)
    capybara (3.40.0)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.11)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    caxlsx (4.3.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 2.4, < 4)
    clearance (2.10.0)
      actionmailer (>= 5.0)
      activemodel (>= 5.0)
      activerecord (>= 5.0)
      argon2 (~> 2.0, >= 2.0.2)
      bcrypt (>= 3.1.1)
      email_validator (~> 2.0)
      railties (>= 5.0)
    climate_control (1.2.0)
    cloudflare-rails (6.2.0)
      actionpack (>= 7.1.0, < 8.1.0)
      activesupport (>= 7.1.0, < 8.1.0)
      railties (>= 7.1.0, < 8.1.0)
      zeitwerk (>= 2.5.0)
    coderay (1.1.3)
    concurrent-ruby (1.3.5)
    conifer (2.0.0)
    connection_pool (2.5.3)
    crack (1.0.0)
      bigdecimal
      rexml
    crass (1.0.6)
    css_parser (1.21.1)
      addressable
    csv (3.3.5)
    cuprite (0.17)
      capybara (~> 3.0)
      ferrum (~> 0.17.0)
    date (3.4.1)
    declarative (0.0.20)
    device_detector (1.1.3)
    diff-lcs (1.6.2)
    docile (1.4.1)
    domain_name (0.6.20240107)
    dotenv (3.1.8)
    drb (2.2.3)
    dry-container (0.11.0)
      concurrent-ruby (~> 1.0)
    dry-core (1.1.0)
      concurrent-ruby (~> 1.0)
      logger
      zeitwerk (~> 2.6)
    dry-inflector (1.2.0)
    dry-initializer (3.2.0)
    dry-logic (1.6.0)
      bigdecimal
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-monads (1.9.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.1)
      zeitwerk (~> 2.6)
    dry-struct (1.8.0)
      dry-core (~> 1.1)
      dry-types (~> 1.8, >= 1.8.2)
      ice_nine (~> 0.11)
      zeitwerk (~> 2.6)
    dry-types (1.8.3)
      bigdecimal (~> 3.0)
      concurrent-ruby (~> 1.0)
      dry-core (~> 1.0)
      dry-inflector (~> 1.0)
      dry-logic (~> 1.4)
      zeitwerk (~> 2.6)
    einhorn (1.0.1)
      fiddle (~> 1.1)
    elastic-transport (8.4.0)
      faraday (< 3)
      multi_json
    elasticsearch (8.19.0)
      elastic-transport (~> 8.3)
      elasticsearch-api (= 8.19.0)
    elasticsearch-api (8.19.0)
      multi_json
    elasticsearch-dsl (0.1.10)
    elasticsearch-model (8.0.0)
      activesupport (> 3)
      elasticsearch (~> 8)
      hashie
    elasticsearch-rails (8.0.0)
    email_validator (2.2.4)
      activemodel
    erb (5.0.2)
    erubi (1.13.1)
    excon (1.3.0)
      logger
    factory_bot (6.5.5)
      activesupport (>= 6.1.0)
    factory_bot_rails (6.5.0)
      factory_bot (~> 6.5)
      railties (>= 6.1.0)
    faker (3.5.2)
      i18n (>= 1.8.11, < 2)
    faraday (2.13.4)
      faraday-net_http (>= 2.0, < 3.5)
      json
      logger
    faraday-follow_redirects (0.3.0)
      faraday (>= 1, < 3)
    faraday-mashify (1.0.0)
      faraday (~> 2.0)
      hashie
    faraday-multipart (1.1.1)
      multipart-post (~> 2.0)
    faraday-net_http (3.4.1)
      net-http (>= 0.5.0)
    ferrum (0.17.1)
      addressable (~> 2.5)
      base64 (~> 0.2)
      concurrent-ruby (~> 1.1)
      webrick (~> 1.7)
      websocket-driver (~> 0.7)
    ffi (1.17.2)
    ffi (1.17.2-x86_64-darwin)
    ffi (1.17.2-x86_64-linux-gnu)
    ffi-compiler (1.3.2)
      ffi (>= 1.15.5)
      rake
    fiddle (1.1.8)
    fog-aws (3.33.0)
      base64 (>= 0.2, < 0.4)
      fog-core (~> 2.6)
      fog-json (~> 1.1)
      fog-xml (~> 0.1)
    fog-core (2.6.0)
      builder
      excon (~> 1.0)
      formatador (>= 0.2, < 2.0)
      mime-types
    fog-json (1.2.0)
      fog-core
      multi_json (~> 1.10)
    fog-xml (0.1.5)
      fog-core
      nokogiri (>= 1.5.11, < 2.0.0)
    formatador (1.2.0)
      reline
    formtastic (5.0.0)
      actionpack (>= 6.0.0)
    formtastic_i18n (0.7.0)
    friendly_id (5.5.1)
      activerecord (>= 4.0.0)
    gem-silencer (0.4.0)
      warning (~> 1.0)
      yaml (~> 0.1)
    get_process_mem (1.0.0)
      bigdecimal (>= 2.0)
      ffi (~> 1.0)
    github-ds (0.5.4)
      activerecord (>= 3.2)
    gli (2.22.2)
      ostruct
    globalid (1.2.1)
      activesupport (>= 6.1)
    google-apis-analyticsdata_v1beta (0.40.0)
      google-apis-core (>= 0.15.0, < 2.a)
    google-apis-core (1.0.1)
      addressable (~> 2.8, >= 2.8.7)
      faraday (~> 2.13)
      faraday-follow_redirects (~> 0.3)
      googleauth (~> 1.14)
      mini_mime (~> 1.1)
      representable (~> 3.0)
      retriable (~> 3.1)
    google-cloud-env (2.3.1)
      base64 (~> 0.2)
      faraday (>= 1.0, < 3.a)
    google-logging-utils (0.2.0)
    googleauth (1.15.0)
      faraday (>= 1.0, < 3.a)
      google-cloud-env (~> 2.2)
      google-logging-utils (~> 0.1)
      jwt (>= 1.4, < 4.0)
      multi_json (~> 1.11)
      os (>= 0.9, < 2.0)
      signet (>= 0.16, < 2.a)
    groupdate (6.7.0)
      activesupport (>= 7.1)
    gserver (0.0.1)
    has_scope (0.8.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
    hashdiff (1.2.0)
    hashery (2.1.2)
    hashie (5.0.0)
    high_voltage (4.0.0)
    highline (3.1.2)
      reline
    hotwire-livereload (2.0.0)
      actioncable (>= 7.0.0)
      listen (>= 3.0.0)
      railties (>= 7.0.0)
    htmlbeautifier (1.4.3)
    htmlentities (4.3.4)
    http (5.3.1)
      addressable (~> 2.8)
      http-cookie (~> 1.0)
      http-form_data (~> 2.2)
      llhttp-ffi (~> 0.5.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    http-form_data (2.3.0)
    httparty (0.23.1)
      csv
      mini_mime (>= 1.0.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.7)
      concurrent-ruby (~> 1.0)
    i18n-tasks (1.0.15)
      activesupport (>= 4.0.2)
      ast (>= 2.1.0)
      erubi
      highline (>= 2.0.0)
      i18n
      parser (>= *******)
      rails-i18n
      rainbow (>= 2.2.2, < 4.0)
      ruby-progressbar (~> 1.8, >= 1.8.1)
      terminal-table (>= 1.5.1)
    ice_nine (0.11.2)
    image_processing (1.14.0)
      mini_magick (>= 4.9.5, < 6)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (2.2.2)
      actionpack (>= 6.0.0)
      activesupport (>= 6.0.0)
      railties (>= 6.0.0)
    inherited_resources (2.1.0)
      actionpack (>= 7.0)
      has_scope (>= 0.6)
      railties (>= 7.0)
      responders (>= 2)
    io-console (0.8.1)
    irb (1.15.2)
      pp (>= 0.6.0)
      rdoc (>= 4.0.0)
      reline (>= 0.4.2)
    jbuilder (2.14.1)
      actionview (>= 7.0.0)
      activesupport (>= 7.0.0)
    jmespath (1.6.2)
    json (2.13.2)
    json-schema (5.2.2)
      addressable (~> 2.8)
      bigdecimal (~> 3.1)
    jsonapi-deserializable (0.2.0)
    jsonapi-parser (0.1.1)
    jsonapi-rails (0.4.1)
      jsonapi-parser (~> 0.1.0)
      jsonapi-rb (~> 0.5.0)
    jsonapi-rb (0.5.0)
      jsonapi-deserializable (~> 0.2.0)
      jsonapi-serializable (~> 0.3.0)
    jsonapi-renderer (0.2.2)
    jsonapi-serializable (0.3.1)
      jsonapi-renderer (~> 0.2.0)
    jwt (3.1.2)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    kramdown (2.5.1)
      rexml (>= 3.3.9)
    kramdown-parser-gfm (1.1.0)
      kramdown (~> 2.0)
    language_server-protocol (********)
    lint_roller (1.1.0)
    listen (3.9.0)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    llhttp-ffi (0.5.1)
      ffi-compiler (~> 1.0)
      rake (~> 13.0)
    logger (1.7.0)
    loofah (2.24.1)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    lookbook (2.3.13)
      activemodel
      css_parser
      htmlbeautifier (~> 1.3)
      htmlentities (~> 4.3.4)
      marcel (~> 1.0)
      railties (>= 5.0)
      redcarpet (~> 3.5)
      rouge (>= 3.26, < 5.0)
      view_component (>= 2.0)
      yard (~> 0.9)
      zeitwerk (~> 2.5)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    maildown (3.3.1)
      actionmailer (>= 4.0.0)
      kramdown-parser-gfm
    marcel (1.0.4)
    matrix (0.4.3)
    method_source (1.1.0)
    mime-types (3.7.0)
      logger
      mime-types-data (~> 3.2025, >= 3.2025.0507)
    mime-types-data (3.2025.0826)
    mini_magick (5.3.1)
      logger
    mini_mime (1.1.5)
    mini_portile2 (2.8.9)
    minitest (5.25.5)
    msgpack (1.8.0)
    multi_json (1.17.0)
    multi_xml (0.7.2)
      bigdecimal (~> 3.1)
    multipart-post (2.4.1)
    naught (1.1.0)
    net-http (0.6.0)
      uri
    net-imap (0.5.9)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.2)
      timeout
    net-smtp (0.5.1)
      net-protocol
    newrelic_rpm (9.20.0)
    nio4r (2.7.4)
    nokogiri (1.18.9)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-darwin)
      racc (~> 1.4)
    nokogiri (1.18.9-x86_64-linux-gnu)
      racc (~> 1.4)
    oauth2 (2.0.12)
      faraday (>= 0.17.3, < 4.0)
      jwt (>= 1.0, < 4.0)
      logger (~> 1.2)
      multi_xml (~> 0.5)
      rack (>= 1.2, < 4)
      snaky_hash (~> 2.0, >= 2.0.3)
      version_gem (>= 1.1.8, < 3)
    omniauth (2.1.3)
      hashie (>= 3.4.6)
      rack (>= 2.2.3)
      rack-protection
    omniauth-facebook (10.0.0)
      bigdecimal
      omniauth-oauth2 (>= 1.2, < 3)
    omniauth-google-oauth2 (1.2.1)
      jwt (>= 2.9.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-linkedin-openid (1.0.2)
      oauth2 (~> 2.0)
      omniauth (~> 2.0)
      omniauth-oauth2 (~> 1.8)
    omniauth-oauth2 (1.8.0)
      oauth2 (>= 1.4, < 3)
      omniauth (~> 2.0)
    omniauth-rails_csrf_protection (1.0.2)
      actionpack (>= 4.2)
      omniauth (~> 2.0)
    one_off (1.4.0)
      rails (>= 6.0.0)
    online_migrations (0.29.3)
      activerecord (>= 7.1)
    os (1.1.4)
    ostruct (0.6.3)
    parallel (1.27.0)
    parser (*******)
      ast (~> 2.4.1)
      racc
    pdf-reader (2.15.0)
      Ascii85 (>= 1.0, < 3.0, != 2.0.0)
      afm (>= 0.2.1, < 2)
      hashery (~> 2.0)
      ruby-rc4
      ttfunk
    pg (1.6.1)
    pg (1.6.1-x86_64-darwin)
    pg (1.6.1-x86_64-linux)
    pp (0.6.2)
      prettyprint
    premailer (1.27.0)
      addressable
      css_parser (>= 1.19.0)
      htmlentities (>= 4.0.0)
    premailer-rails (1.12.0)
      actionmailer (>= 3)
      net-smtp
      premailer (~> 1.7, >= 1.7.9)
    prettyprint (0.2.0)
    prism (1.4.0)
    propshaft (1.2.1)
      actionpack (>= 7.0.0)
      activesupport (>= 7.0.0)
      rack
    pry (0.15.2)
      coderay (~> 1.1)
      method_source (~> 1.0)
    pry-rails (0.3.11)
      pry (>= 0.13.0)
    psych (5.2.6)
      date
      stringio
    public_suffix (6.0.2)
    puma (6.6.1)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    racc (1.8.1)
    rack (3.2.0)
    rack-attack (6.7.0)
      rack (>= 1.0, < 4)
    rack-canonical-host (1.3.0)
      addressable (> 0, < 3)
      rack (>= 1.6, < 4)
    rack-cors (3.0.0)
      logger
      rack (>= 3.0.14)
    rack-protection (4.1.1)
      base64 (>= 0.1.0)
      logger (>= 1.6.0)
      rack (>= 3.0.0, < 4)
    rack-proxy (0.7.7)
      rack
    rack-session (2.1.1)
      base64 (>= 0.1.0)
      rack (>= 3.0.0)
    rack-strip-cookies (2.0.0)
      rack (>= 3.0)
    rack-test (2.2.0)
      rack (>= 1.3)
    rack-utm (0.0.2)
      rack
    rackup (2.2.1)
      rack (>= 3)
    rails (*******)
      actioncable (= *******)
      actionmailbox (= *******)
      actionmailer (= *******)
      actionpack (= *******)
      actiontext (= *******)
      actionview (= *******)
      activejob (= *******)
      activemodel (= *******)
      activerecord (= *******)
      activestorage (= *******)
      activesupport (= *******)
      bundler (>= 1.15.0)
      railties (= *******)
    rails-dom-testing (2.3.0)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.2)
      loofah (~> 2.21)
      nokogiri (>= 1.15.7, != 1.16.7, != 1.16.6, != 1.16.5, != 1.16.4, != 1.16.3, != 1.16.2, != 1.16.1, != 1.16.0.rc1, != 1.16.0)
    rails-i18n (8.0.2)
      i18n (>= 0.7, < 2)
      railties (>= 8.0.0, < 9)
    railties (*******)
      actionpack (= *******)
      activesupport (= *******)
      irb (~> 1.13)
      rackup (>= 1.0.0)
      rake (>= 12.2)
      thor (~> 1.0, >= 1.2.2)
      zeitwerk (~> 2.6)
    rainbow (3.1.1)
    rake (13.3.0)
    ranked-model (0.4.11)
      activerecord (>= 5.2)
    ransack (4.3.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.11.1)
      ffi (~> 1.0)
    rdoc (6.14.2)
      erb
      psych (>= 4.0.0)
    readme-metrics (2.4.1)
      httparty (~> 0.18)
      rack (>= 2.2, < 4)
    recaptcha (5.20.1)
    redcarpet (3.6.1)
    redis-client (0.25.2)
      connection_pool
    redlock (2.0.6)
      redis-client (>= 0.14.1, < 1.0.0)
    regexp_parser (2.11.2)
    reline (0.6.2)
      io-console (~> 0.5)
    representable (3.2.0)
      declarative (< 0.1.0)
      trailblazer-option (>= 0.1.1, < 0.2.0)
      uber (< 0.2.0)
    responders (3.1.1)
      actionpack (>= 5.2)
      railties (>= 5.2)
    retriable (3.1.2)
    rexml (3.4.2)
    rollups (0.5.0)
      activesupport (>= 7.1)
      groupdate (>= 6.1)
    rouge (4.6.0)
    rspec-core (3.13.5)
      rspec-support (~> 3.13.0)
    rspec-expectations (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-mocks (3.13.5)
      diff-lcs (>= 1.2.0, < 2.0)
      rspec-support (~> 3.13.0)
    rspec-rails (8.0.2)
      actionpack (>= 7.2)
      activesupport (>= 7.2)
      railties (>= 7.2)
      rspec-core (~> 3.13)
      rspec-expectations (~> 3.13)
      rspec-mocks (~> 3.13)
      rspec-support (~> 3.13)
    rspec-support (3.13.5)
    rspec_junit_formatter (0.6.0)
      rspec-core (>= 2, < 4, != 2.12.0)
    rswag-specs (2.16.0)
      activesupport (>= 5.2, < 8.1)
      json-schema (>= 2.2, < 6.0)
      railties (>= 5.2, < 8.1)
      rspec-core (>= 2.14)
    rubocop (1.80.1)
      json (~> 2.3)
      language_server-protocol (~> 3.17.0.2)
      lint_roller (~> 1.1.0)
      parallel (~> 1.10)
      parser (>= 3.3.0.2)
      rainbow (>= 2.2.2, < 4.0)
      regexp_parser (>= 2.9.3, < 3.0)
      rubocop-ast (>= 1.46.0, < 2.0)
      ruby-progressbar (~> 1.7)
      unicode-display_width (>= 2.4.0, < 4.0)
    rubocop-ast (1.46.0)
      parser (>= 3.3.7.2)
      prism (~> 1.4)
    rubocop-capybara (2.22.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-factory_bot (2.27.1)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    rubocop-rails (2.33.3)
      activesupport (>= 4.2.0)
      lint_roller (~> 1.1)
      rack (>= 1.1)
      rubocop (>= 1.75.0, < 2.0)
      rubocop-ast (>= 1.44.0, < 2.0)
    rubocop-rspec (3.6.0)
      lint_roller (~> 1.1)
      rubocop (~> 1.72, >= 1.72.1)
    ruby-progressbar (1.13.0)
    ruby-rc4 (0.1.5)
    ruby-vips (2.2.5)
      ffi (~> 1.12)
      logger
    rubyzip (3.0.2)
    safely_block (0.5.0)
    scenic (1.9.0)
      activerecord (>= 4.0.0)
      railties (>= 4.0.0)
    securerandom (0.4.1)
    shoulda-matchers (6.5.0)
      activesupport (>= 5.2.0)
    sidekiq (8.0.7)
      connection_pool (>= 2.5.0)
      json (>= 2.9.0)
      logger (>= 1.6.2)
      rack (>= 3.1.0)
      redis-client (>= 0.23.2)
    signet (0.21.0)
      addressable (~> 2.8)
      faraday (>= 0.17.5, < 3.a)
      jwt (>= 1.5, < 4.0)
      multi_json (~> 1.10)
    simplecov (0.22.0)
      docile (~> 1.1)
      simplecov-html (~> 0.11)
      simplecov_json_formatter (~> 0.1)
    simplecov-cobertura (3.1.0)
      rexml
      simplecov (~> 0.19)
    simplecov-html (0.13.2)
    simplecov_json_formatter (0.1.4)
    sitemap_generator (6.3.0)
      builder (~> 3.0)
    slack-ruby-client (2.7.0)
      faraday (>= 2.0.1)
      faraday-mashify
      faraday-multipart
      gli
      hashie
      logger
    snaky_hash (2.0.3)
      hashie (>= 0.1.0, < 6)
      version_gem (>= 1.1.8, < 3)
    stimulus-rails (1.3.4)
      railties (>= 6.0.0)
    store_model (4.3.0)
      activerecord (>= 7.0)
    stringio (3.1.7)
    stripe (15.4.0)
    svg_optimizer (0.3.0)
      nokogiri
    terminal-table (4.0.0)
      unicode-display_width (>= 1.1.1, < 4)
    thor (1.4.0)
    timeout (0.4.3)
    trailblazer-option (0.1.2)
    ttfunk (1.8.0)
      bigdecimal (~> 3.1)
    turbo-rails (2.0.16)
      actionpack (>= 7.1.0)
      railties (>= 7.1.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uber (0.1.0)
    unf (0.2.0)
    unicode-display_width (3.1.5)
      unicode-emoji (~> 4.0, >= 4.0.4)
    unicode-emoji (4.0.4)
    uri (1.0.3)
    useragent (0.16.11)
    version_gem (1.1.8)
    view_component (4.0.2)
      activesupport (>= 7.1.0, < 8.1)
      concurrent-ruby (~> 1)
    warning (1.5.0)
    webmock (3.25.1)
      addressable (>= 2.8.0)
      crack (>= 0.3.2)
      hashdiff (>= 0.4.0, < 2.0.0)
    webrick (1.9.1)
    websocket-driver (0.8.0)
      base64
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    wisper (3.0.0)
    with_model (2.2.0)
      activerecord (>= 7.0)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    yaml (0.4.0)
    yard (0.9.37)
    zeitwerk (2.7.3)

PLATFORMS
  ruby
  x86_64-darwin
  x86_64-darwin-22
  x86_64-linux

DEPENDENCIES
  aasm
  activeadmin (= 4.0.0.beta16)!
  after_commit_everywhere
  ahoy_matey!
  annotate!
  asset_sync
  audited
  aws-sdk-cloudwatch
  aws-sdk-s3
  aws-sdk-sesv2
  baran
  bcrypt
  blueprinter
  bootsnap
  browser
  bulk_insert
  capybara
  caxlsx
  clearance
  climate_control
  cloudflare-rails
  conifer
  csv
  cuprite
  dotenv
  dry-container
  dry-initializer
  dry-monads
  dry-struct
  elasticsearch-dsl
  elasticsearch-model
  elasticsearch-rails
  factory_bot_rails
  faker
  fog-aws
  friendly_id
  gem-silencer
  github-ds
  google-apis-analyticsdata_v1beta
  high_voltage
  hotwire-livereload
  http
  i18n-tasks
  image_processing
  importmap-rails
  jbuilder
  jsonapi-rails
  kaminari
  lookbook
  maildown
  naught
  newrelic_rpm
  omniauth-facebook
  omniauth-google-oauth2
  omniauth-linkedin-openid
  omniauth-rails_csrf_protection
  one_off
  online_migrations
  pdf-reader
  pg
  premailer-rails
  propshaft
  pry-rails
  puma
  pundit
  rack-attack
  rack-canonical-host
  rack-cors
  rack-reverse-proxy!
  rack-strip-cookies
  rack-utm
  rails (= *******)
  ranked-model
  readme-metrics
  recaptcha
  redlock
  rollups
  rspec-rails
  rspec_junit_formatter
  rswag-specs
  rubocop
  rubocop-capybara
  rubocop-factory_bot
  rubocop-rails
  rubocop-rspec
  scenic
  shoulda-matchers
  sidekiq
  sidekiq-ent!
  sidekiq-pro!
  simplecov
  simplecov-cobertura
  sitemap_generator
  slack-ruby-client
  stimulus-rails
  store_model
  stripe
  svg_optimizer
  turbo-rails
  view_component
  warning
  webmock
  wisper
  with_model

RUBY VERSION
   ruby 3.4.5p51

BUNDLED WITH
   2.7.0
