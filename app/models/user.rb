# == Schema Information
#
# Table name: users
#
#  id                   :bigint           not null, primary key
#  admin                :boolean          default(FALSE), not null
#  blocked              :boolean          default(FALSE), not null
#  company              :string
#  confirmation_token   :string
#  current_login_ip     :inet
#  email                :citext           not null
#  encrypted_password   :string
#  first_name           :string           not null
#  last_name            :string           not null
#  phone                :string
#  remember_token       :string           not null
#  uuid                 :uuid             not null
#  verification_token   :string
#  verified             :boolean          default(FALSE), not null
#  created_at           :datetime         not null
#  updated_at           :datetime         not null
#  analytics_company_id :bigint
#  stripe_customer_id   :string
#
# Indexes
#
#  index_users_on_analytics_company_id  (analytics_company_id)
#  index_users_on_email                 (email) UNIQUE
#  index_users_on_remember_token        (remember_token)
#  index_users_on_uuid                  (uuid) UNIQUE
#
class User < ApplicationRecord
  include Clearance::User
  include Clearance::EmailVerification
  include Wisper::ActiveRecord::Publisher

  normalizes :first_name, :last_name, :company, :phone, with: Functions['normalizers.strip']

  belongs_to :analytics_company, class_name: 'Analytics::Company', optional: true

  has_one_attached :avatar

  has_one :access_package, -> { where(active: true) }, as: :resource, inverse_of: :resource, dependent: :destroy

  has_one :broker, -> { broker }, class_name: 'Persona', inverse_of: :user, dependent: :destroy
  has_one :carrier, -> { carrier }, class_name: 'Persona', inverse_of: :user, dependent: :destroy
  has_one :shipper, -> { shipper }, class_name: 'Persona', inverse_of: :user, dependent: :destroy
  has_one :dispatcher, -> { dispatcher }, class_name: 'Persona', inverse_of: :user, dependent: :destroy
  has_one :seller, -> { seller }, class_name: 'Persona', inverse_of: :user, dependent: :destroy
  has_one :driver, -> { driver }, class_name: 'Persona', inverse_of: :user, dependent: :destroy

  has_one :developer_account_user, dependent: :destroy

  has_many :subscriptions, as: :resource, dependent: :restrict_with_error
  has_one :subscription, -> { where(status: 'active') }, inverse_of: :resource, dependent: :restrict_with_error

  has_many :analytics_events, class_name: 'Analytics::Event', dependent: :delete_all
  has_many :analytics_visits, class_name: 'Analytics::Visit', dependent: :delete_all

  has_many :authentications, dependent: :destroy
  has_many :lists, dependent: :destroy
  has_many :personas, dependent: :destroy
  has_many :reviews, dependent: :restrict_with_error

  has_many :user_emails, dependent: :destroy

  has_many :user_roles, dependent: :destroy
  has_many :roles, through: :user_roles
  has_many :role_actions, through: :roles

  has_many :carrier_profile_users, dependent: :destroy
  has_many :carrier_profiles, through: :carrier_profile_users
  has_many :carriers, through: :carrier_profiles

  has_many :brokerage_profile_users, dependent: :destroy
  has_many :brokerage_profiles, through: :brokerage_profile_users
  has_many :brokerages, through: :brokerage_profiles

  validates :first_name, :last_name, presence: true, length: { maximum: 30 }, name: true
  validates :password, password: true
  validates :email, email_domain: true

  def name
    [first_name, last_name].join(' ')
  end

  def persona(scope: :any, verified: nil)
    Users::Personas.find(user: self, scope:).then do |persona|
      if verified.nil?
        persona
      else
        persona&.verified? == verified ? persona : nil
      end
    end
  end

  def persona?(scope: :any, verified: nil)
    persona(scope:, verified:).present?
  end

  def monogram
    [first_name[0], last_name[0]].join.upcase
  end

  def upgraded?
    access_package.present?
  end

  private

  # Setting this method to true to allow other forms of user authentication, e.g. LinkedIn
  #
  # @return [true]
  def password_optional?
    true
  end
end
