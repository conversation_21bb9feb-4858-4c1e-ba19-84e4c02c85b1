import { Controller } from '@hotwired/stimulus'

export default class extends Controller {
  static values = {
    endpoint: String,
    interval: { type: Number, default: 2000 }
  }

  connect() {
    this.startPolling()
  }

  disconnect() {
    this.stopPolling()
  }

  startPolling() {
    this.pollingInterval = setInterval(() => {
      this.fetchUpdates()
    }, this.intervalValue)
  }

  stopPolling() {
    clearInterval(this.pollingInterval)
  }

  async fetchUpdates() {
    try {
      const response = await fetch(this.endpointValue, {
        headers: { Accept: 'text/vnd.turbo-stream.html' }
      })

      if (response.ok && response.status !== 204) {
        this.stopPolling()
        const turboStreamContent = await response.text()
        window.Turbo.renderStreamMessage(turboStreamContent)
      }
    } catch (error) {
      console.error('Polling error:', error)
    }
  }
}
