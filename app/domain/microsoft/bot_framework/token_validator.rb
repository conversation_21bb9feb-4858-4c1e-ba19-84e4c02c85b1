module Microsoft
  module BotFramework
    class TokenValidator
      attr_reader :token, :expected_aud, :tenant_id

      def initialize(auth_header:,
                     expected_aud: CarrierSource::Credentials.lookup(:teams, :client_id),
                     tenant_id: CarrierSource::Credentials.lookup(:teams, :tenant_id))
        @token = auth_header.to_s[/\ABearer\s+(.+)\z/i, 1]
        @expected_aud = expected_aud
        @tenant_id = tenant_id
      end

      def valid?
        return false if token.blank?

        JWT.decode(
          token, nil, true,
          { algorithm: 'RS256', jwks:, aud: expected_aud, verify_aud: true, iss: issuers, verify_iss: true }
        )
      rescue JWT::DecodeError => e
        Rails.logger.warn("[Microsoft::BotFramework::TokenValidator] invalid token: #{e.class} #{e.message}")
        false
      end

      private

      def jwks
        Jwks.new(token:, tenant_id:).fetch
      end

      def issuers
        ['https://api.botframework.com', "https://sts.windows.net/#{tenant_id}/"]
      end
    end
  end
end
