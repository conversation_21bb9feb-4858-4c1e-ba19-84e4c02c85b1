module Microsoft
  module BotFramework
    class Jwks
      OPENID_CONFIG_URI = 'https://login.botframework.com/v1/.well-known/openid-configuration'.freeze

      attr_reader :token, :tenant_id

      def initialize(token:, tenant_id: CarrierSource::Credentials.lookup(:teams, :tenant_id))
        @token = token
        @tenant_id = tenant_id
      end

      def fetch
        HTTP.get(openid_configuration.fetch('jwks_uri')).parse.fetch('keys')
      end

      def openid_configuration
        HTTP.get(openid_config_uri).parse
      end

      private

      def openid_config_uri
        {
          "https://sts.windows.net/#{tenant_id}/" => tenant_openid_config_uri
        }.fetch(payload['iss'], OPENID_CONFIG_URI)
      end

      def tenant_openid_config_uri
        "https://login.microsoftonline.com/#{tenant_id}/v2.0/.well-known/openid-configuration"
      end

      def payload
        @payload ||= JWT.decode(token, nil, false).first
      end
    end
  end
end
