const generate = require('maps/generate')
const { DOM<PERSON>ars<PERSON> } = require('@xmldom/xmldom')

describe('generate', () => {
  it('returns a svg image', () => {
    const input = {
      crs: '+proj=lcc +lat_1=33 +lat_2=45 +lat_0=39 +lon_0=-96 +x_0=0 +y_0=0 +datum=NAD83 +units=m +no_defs',
      states: [
        'united-states:illinois',
        'united-states:oklahoma',
        'united-states:tennessee',
        'united-states:missouri',
        'united-states:arkansas',
        'united-states:georgia',
        'united-states:kentucky'
      ],
      headquarters: {
        name: 'Chicago, IL',
        coordinates: [-87.6722782679966, 41.873859723409]
      },
      maps: ['united-states'],
      terminals: [
        {
          name: 'Tulsa, OK',
          coordinates: [-95.9419987018292, 36.1342973709106]
        },
        {
          name: 'Nashville, TN',
          coordinates: [-86.7797100360577, 36.1462154877491]
        }
      ]
    }

    const parser = new DOMParser()
    const output = generate(input)
    const doc = parser.parseFromString(output, 'image/svg+xml')
    expect(doc.documentElement.nodeName).toBe('svg')
  })
})
