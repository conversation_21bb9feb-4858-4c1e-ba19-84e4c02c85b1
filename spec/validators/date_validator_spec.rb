require 'rails_helper'

RSpec.describe DateValidator do
  let(:model) do
    Class.new do
      include ActiveModel::Model
      include ActiveModel::Validations

      attr_accessor :start_date, :end_date

      validates :start_date, date: { before: :end_date }
    end
  end

  let(:instance) { model.new(start_date:, end_date:) }

  context 'when start date is before end date' do
    let(:start_date) { 1.month.ago }
    let(:end_date) { 1.month.from_now }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end

  context 'when start date is after end date' do
    let(:start_date) { 1.month.from_now }
    let(:end_date) { 1.month.ago }

    it 'is invalid' do
      expect(instance).not_to be_valid
    end
  end

  context 'when start date is equal to end date' do
    let(:start_date) { 1.month.from_now }
    let(:end_date) { 1.month.from_now }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end

  context 'when start date is nil' do
    let(:start_date) { nil }
    let(:end_date) { 1.month.from_now }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end

  context 'when end date is nil' do
    let(:start_date) { 1.month.from_now }
    let(:end_date) { nil }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end
end
