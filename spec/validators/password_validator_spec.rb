require 'rails_helper'

RSpec.describe PasswordValidator do
  let(:model) do
    Class.new do
      include ActiveModel::Model
      include ActiveModel::Validations

      attr_accessor :password

      validates :password, password: true
    end
  end

  let(:instance) { TestModel.new(password:) }

  before do
    stub_const('TestModel', model)
  end

  shared_examples 'a password validator' do |value|
    let(:password) { value }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end

  shared_examples 'a password invalidator' do |value|
    let(:password) { value }

    it 'is invalid' do
      expect(instance).not_to be_valid
    end
  end

  it_behaves_like 'a password validator', 'Password1!'
  it_behaves_like 'a password validator', 'Password1!@#$%^&*()_+-=~`[]{}|;:\'",.<>?/\\'

  it_behaves_like 'a password invalidator', 'password'
  it_behaves_like 'a password invalidator', 'PASSWORD'
  it_behaves_like 'a password invalidator', 'Password'
  it_behaves_like 'a password invalidator', 'Password1'
  it_behaves_like 'a password invalidator', 'Password1 '
end
