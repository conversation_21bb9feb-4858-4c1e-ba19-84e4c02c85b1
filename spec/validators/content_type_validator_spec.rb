require 'rails_helper'

RSpec.describe ContentTypeValidator do
  with_model :Person do
    model do
      has_one_attached :avatar
      has_one_attached :document
      has_one_attached :file

      validates :avatar, content_type: { start_with: %w(image) }
      validates :document, content_type: { in: %w(application/pdf) }
    end
  end

  let(:person) { Person.new }

  describe 'validations' do
    context 'when the content type is valid' do
      it 'is valid for image content type' do
        person.avatar.attach(io: StringIO.new('image data'), filename: 'avatar.png', content_type: 'image/png')
        expect(person).to be_valid
      end

      it 'is valid for PDF content type' do
        person.document.attach(io: StringIO.new('pdf data'), filename: 'document.pdf', content_type: 'application/pdf')
        expect(person).to be_valid
      end
    end

    context 'when the content type is invalid' do
      it 'is invalid for non-image content type' do
        person.avatar.attach(io: StringIO.new('text data'), filename: 'avatar.txt', content_type: 'text/plain')
        expect(person).not_to be_valid
        expect(person.errors[:avatar]).to include('is not a valid file format')
      end

      it 'is invalid for non-PDF content type' do
        person.document.attach(io: StringIO.new('text data'), filename: 'document.txt', content_type: 'text/plain')
        expect(person).not_to be_valid
        expect(person.errors[:document]).to include('is not a valid file format')
      end
    end

    context 'when the attachment is not present' do
      it 'is valid when no file is attached' do
        expect(person).to be_valid
      end
    end

    context 'when the validator is incorrectly configured' do
      it 'raises an error if both :in and :start_with are provided' do
        expect do
          Person.validates :file, content_type: { in: %w(image/png), start_with: %w(image) }
        end.to raise_error(ArgumentError, /Either :in or :start_with must be supplied/)
      end

      it 'raises an error if neither :in nor :start_with are provided' do
        expect do
          Person.validates :file, content_type: {}
        end.to raise_error(ArgumentError, /Either :in or :start_with must be supplied/)
      end
    end
  end
end
