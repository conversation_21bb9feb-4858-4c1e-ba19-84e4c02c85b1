require 'rails_helper'

RSpec.describe NameValidator do
  let(:model) do
    Class.new do
      include ActiveModel::Model
      include ActiveModel::Validations

      attr_accessor :first_name

      validates :first_name, name: true
    end
  end

  let(:instance) { TestModel.new(first_name:) }

  before do
    stub_const('TestModel', model)
  end

  shared_examples 'a name validator' do |name|
    let(:first_name) { name }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end

  shared_examples 'a name invalidator' do |name|
    let(:first_name) { name }

    it 'is invalid' do
      expect(instance).not_to be_valid
    end
  end

  it_behaves_like 'a name validator', '<PERSON>'
  it_behaves_like 'a name validator', 'm'
  it_behaves_like 'a name validator', '<PERSON>.'
  it_behaves_like 'a name validator', '<PERSON>-<PERSON><PERSON>'
  it_behaves_like 'a name validator', "<PERSON>'<PERSON>"
  it_behaves_like 'a name validator', '<PERSON>'
  it_behaves_like 'a name validator', '<PERSON><PERSON><PERSON>ø<PERSON>'
  it_behaves_like 'a name validator', 'فلسطين'
  it_behaves_like 'a name validator', 'Владимир'

  it_behaves_like 'a name invalidator', 'Mike1'
  it_behaves_like 'a name invalidator', 'm-'
  it_behaves_like 'a name invalidator', "Mark O'"
  it_behaves_like 'a name invalidator', 'm_m'
  it_behaves_like 'a name invalidator', 'evil.com'
end
