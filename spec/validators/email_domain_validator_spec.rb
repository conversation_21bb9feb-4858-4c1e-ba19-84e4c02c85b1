require 'rails_helper'

RSpec.describe EmailDomainValidator do
  let(:model) do
    Class.new do
      include ActiveModel::Model
      include ActiveModel::Validations

      attr_accessor :email

      validates :email, email_domain: true
    end
  end

  let(:instance) { model.new(email:) }

  before do
    create :email_domain, domain: 'jollyfree.com', disposable: true, free: true
    create :email_domain, domain: 'gmail.com', disposable: false, free: true
  end

  context 'when email domain is disposable' do
    let(:email) { '<EMAIL>' }

    it 'is invalid' do
      expect(instance).not_to be_valid
    end
  end

  context 'when email domain is not disposable' do
    let(:email) { '<EMAIL>' }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end

  context 'when email domain is not in the database' do
    let(:email) { '<EMAIL>' }

    it 'is valid' do
      expect(instance).to be_valid
    end
  end
end
