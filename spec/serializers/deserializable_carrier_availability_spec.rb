require 'rails_helper'

RSpec.describe DeserializableCarrierAvailability do
  subject(:deserializable) { described_class.new(payload) }

  let(:payload) { { 'type' => 'capacities', 'attributes' => attributes } }

  describe '#truck_type_id' do
    context 'when id is a uuid' do
      let(:attributes) { { 'truck_type_id' => truck_types(:flatbed).uuid } }

      it 'returns a truck type id' do
        expect(deserializable.to_hash).to match hash_including('truck_type_id' => truck_types(:flatbed).id)
      end
    end

    context 'when id is a slug' do
      let(:attributes) { { 'truck_type_id' => truck_types(:flatbed).slug } }

      it 'returns a truck type id' do
        expect(deserializable.to_hash).to match hash_including('truck_type_id' => truck_types(:flatbed).id)
      end
    end
  end

  describe '#shipment_type_id' do
    let(:attributes) { { 'shipment_type_id' => shipment_types(:ltl).uuid } }

    it 'returns a shipment type id' do
      expect(deserializable.to_hash).to match hash_including('shipment_type_id' => shipment_types(:ltl).id)
    end
  end

  describe '#origin_city_ids' do
    let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
    let(:attributes) { { 'origin_city_ids' => city.full_slug } }

    it 'returns an array of city ids' do
      expect(deserializable.to_hash).to match hash_including('origin_city_ids' => [city.id])
    end
  end

  describe '#destination_city_ids' do
    let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
    let(:attributes) { { 'destination_city_ids' => city.full_slug } }

    it 'returns an array of city ids' do
      expect(deserializable.to_hash).to match hash_including('destination_city_ids' => [city.id])
    end
  end
end
