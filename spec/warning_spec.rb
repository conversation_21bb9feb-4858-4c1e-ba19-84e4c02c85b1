require 'rails_helper'

RSpec.describe Warning do
  context 'when message contains deprecation' do
    it 'raises an error' do
      expect { warn('[DEPRECATION WARNING] This method is dead!') }.to raise_error ActiveSupport::DeprecationException
    end
  end

  context 'when message does not contain deprecation' do
    it 'does not raise an error' do
      expect { warn('This is a regular warning.') }.to output("This is a regular warning.\n").to_stderr
    end
  end
end
