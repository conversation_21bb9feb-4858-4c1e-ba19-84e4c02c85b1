require 'rails_helper'

RSpec.configure do |config|
  # Specify a root folder where Swagger JSON files are generated
  # NOTE: If you're using the rswag-api to serve API descriptions, you'll need
  # to ensure that it's configured to serve Swagger from the same folder
  config.openapi_root = Rails.root.join('tmp/openapi').to_s

  # Define one or more Swagger documents and provide global metadata for each one
  # When you run the 'rswag:specs:swaggerize' rake task, the complete Swagger will
  # be generated at the provided relative path under swagger_root
  # By default, the operations defined in spec files are added to the first
  # document below. You can override this behavior by adding an openapi_spec tag to
  # the root example_group in your specs, e.g. describe '...', openapi_spec: 'v2/swagger.json'
  config.openapi_specs = {
    'v1/openapi.yaml' => {
      openapi: '3.0.1',
      info: {
        title: 'CarrierSource API',
        version: 'v1'
      },
      components: {
        securitySchemes: {
          bearerAuth: {
            type: :http,
            scheme: :bearer
          }
        },
        schemas: {
          error: {
            type: :object,
            properties: {
              status: { type: :integer },
              title: { type: :string },
              detail: { type: :string },
              source: {
                type: :object,
                properties: {
                  pointer: { type: :string },
                  parameter: { type: :string },
                  header: { type: :string }
                }
              },
              meta: { type: :object }
            }
          },
          capacity: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(capacities) },
              attributes: {
                type: :object,
                properties: {
                  date: { type: :string, format: 'date' },
                  length: { type: :integer, nullable: true },
                  weight: { type: :integer, nullable: true },
                  origin_type: { type: :string, enum: %w(city region state) },
                  destination_type: { type: :string, enum: %w(city region state anywhere) },
                  origin_city_ids: { type: :array, items: { type: :string } },
                  destination_city_ids: { type: :array, items: { type: :string } },
                  origin_state_ids: { type: :array, items: { type: :string } },
                  destination_state_ids: { type: :array, items: { type: :string } },
                  origin_region_ids: { type: :array, items: { type: :string } },
                  destination_region_ids: { type: :array, items: { type: :string } }
                }
              }
            }
          },
          carrier: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(carriers) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string },
                  legal_name: { type: :string, nullable: true },
                  dba_name: { type: :string, nullable: true },
                  dot_number: { type: :integer },
                  slug: { type: :string },
                  safety_rating: { type: :string, enum: %w(satisfactory conditional unsatisfactory), nullable: true },
                  carrier_operation: { type: :string, enum: %w(interstate intrastate), nullable: true },
                  physical_address: {
                    type: :object,
                    properties: {
                      street: { type: :string, nullable: true },
                      city: { type: :string },
                      state: { type: :string },
                      zip: { type: :string }
                    }
                  },
                  fleet_size: { type: :integer, nullable: true },
                  review_count: { type: :integer },
                  star_rating: { type: :number },
                  profile_url: { type: :string },
                  website_url: { type: :string, nullable: true },
                  contact_name: { type: :string, nullable: true },
                  email: { type: :string, nullable: true },
                  phone: { type: :string, nullable: true },
                  claimed: { type: :boolean },
                  sentiments: {
                    type: :array,
                    items: {
                      type: :object,
                      properties: {
                        id: { type: :string },
                        label: { type: :string },
                        positive: { type: :boolean },
                        count: { type: :integer }
                      }
                    }
                  },
                  ratings: {
                    type: :object,
                    properties: {
                      cleanliness: { type: :number, nullable: true },
                      communication: { type: :number, nullable: true },
                      timeliness: { type: :number, nullable: true }
                    }
                  }
                }
              }
            }
          },
          city: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(cities) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string }
                }
              }
            }
          },
          country: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(countries) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string },
                  alpha2: { type: :string }
                }
              }
            }
          },
          freight: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(freights) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string },
                  slug: { type: :string }
                }
              }
            }
          },
          insurance: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(insurances) },
              attributes: {
                type: :object,
                properties: {
                  docket_number: { type: :string },
                  policy_limit: { type: :integer },
                  insurance_type: { type: :string, enum: %w(bipd cargo bond trust_fund bipd_primary bipd_excess) }
                }
              }
            }
          },
          operating_authority: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(operating_authorities) },
              attributes: {
                type: :object,
                properties: {
                  docket_number: { type: :string },
                  common_authority: { type: :string, enum: %w(active inactive none) },
                  contract_authority: { type: :string, enum: %w(active inactive none) },
                  common_authority_date: { type: :string, format: 'date', nullable: true },
                  contract_authority_date: { type: :string, format: 'date', nullable: true }
                }
              }
            }
          },
          region: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(regions) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string }
                }
              }
            }
          },
          review: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(reviews) },
              attributes: {
                type: :object,
                properties: {
                  title: { type: :string },
                  experience: { type: :string },
                  star_rating: { type: :number },
                  timeliness: { type: :number },
                  cleanliness: { type: :number, nullable: true },
                  communication: { type: :number },
                  submitted_at: { type: :string, format: 'date-time' }
                }
              }
            }
          },
          shipment_type: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(shipment_types) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string },
                  slug: { type: :string }
                }
              }
            }
          },
          state: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(states) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string },
                  abbr: { type: :string }
                }
              }
            }
          },
          truck_type: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(truck_types) },
              attributes: {
                type: :object,
                properties: {
                  name: { type: :string },
                  slug: { type: :string }
                }
              }
            }
          },
          user: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(users) },
              attributes: {
                type: :object,
                properties: {
                  first_name: { type: :string },
                  last_name: { type: :string },
                  email: { type: :string }
                }
              }
            }
          },
          webhook_subscription: {
            type: :object,
            properties: {
              id: { type: :string },
              type: { type: :string, enum: %w(webhook_subscriptions) },
              attributes: {
                type: :object,
                properties: {
                  url: { type: :string },
                  secret: { type: :string },
                  types: { type: :array, items: { type: :string, enum: Webhooks::SampleEvent.keys } }
                }
              }
            }
          }
        }
      },
      paths: {},
      servers: [
        {
          url: 'https://api.carriersource.io',
          description: 'Production'
        },
        {
          url: 'https://staging-api.carriersource.io',
          description: 'Staging'
        }
      ]
    }
  }

  # Specify the format of the output Swagger file when running 'rswag:specs:swaggerize'.
  # The openapi_specs configuration option has the filename including format in
  # the key, this may want to be changed to avoid putting yaml in json files.
  # Defaults to json. Accepts ':json' and ':yaml'.
  config.openapi_format = :yaml
end
