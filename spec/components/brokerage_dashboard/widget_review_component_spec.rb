require 'rails_helper'

RSpec.describe BrokerageDashboard::WidgetReviewComponent, type: :component do
  subject(:component) { described_class.new(widget, review) }

  let(:widget) { create :widget, :broker }

  context 'when review is pending' do
    let(:review) { create :brokerage_review, :carrier, :pending }

    it 'does not render' do
      render_inline component
      expect(page.tag_name).to be_blank
      expect(page.text).to be_blank
    end
  end

  context 'when review is approved' do
    let(:review) { create :brokerage_review, :carrier, :approved }

    it 'renders' do
      render_inline component
      expect(page).to have_text 'Widget'
    end
  end
end
