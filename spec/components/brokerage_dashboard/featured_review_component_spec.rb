require 'rails_helper'

RSpec.describe BrokerageDashboard::FeaturedReviewComponent, type: :component do
  subject(:component) { described_class.new(review) }

  context 'when featured' do
    let(:review) { build_stubbed :brokerage_review, :carrier, featured: true }

    it 'renders solid svg' do
      render_inline component
      expect(page).to have_css '.fill-primary'
    end
  end

  context 'when not featured' do
    let(:review) { build_stubbed :brokerage_review, :carrier, featured: false }

    it 'renders regular svg' do
      render_inline component
      expect(page).to have_no_css '.fill-primary'
    end
  end
end
