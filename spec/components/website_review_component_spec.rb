require 'rails_helper'

RSpec.describe WebsiteReviewComponent, type: :component do
  subject(:component) { described_class.new(website, review) }

  let(:website) { create :carrier_profile_website }

  context 'when review is approved' do
    let(:review) { create :review, :approved }

    it 'renders' do
      render_inline component
      expect(page).to have_text 'Website'
    end
  end

  context 'when review is not approved' do
    let(:review) { create :review, :rejected }

    it 'does not render' do
      render_inline component
      expect(page).to have_no_text 'Website'
    end
  end
end
