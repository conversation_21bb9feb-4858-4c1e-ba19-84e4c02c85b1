require 'rails_helper'

RSpec.describe FlashComponent, type: :component do
  let(:component) { described_class.new(flash) }

  describe '::from_errors' do
    let(:bookmark) { build_stubbed :bookmark, list: nil }

    it 'instantiates component from errors' do
      expect(bookmark).not_to be_valid

      render_inline described_class.from_errors(bookmark.errors)
      expect(page).to have_text 'List must exist'
    end
  end

  context 'with alert type' do
    let(:flash) { [[:alert, 'Bad credentials']] }

    it 'renders with correct attributes' do
      render_inline(component)
      expect(page).to have_css 'svg.fill-secondary'
      expect(page).to have_text 'Bad credentials'
    end
  end

  context 'with notice type' do
    let(:flash) { [[:notice, 'Success']] }

    it 'renders with correct attributes' do
      render_inline(component)
      expect(page).to have_css 'svg.fill-green-700'
      expect(page).to have_text 'Success'
    end
  end

  context 'with invalid type' do
    let(:flash) { [[:invalid, 'Error']] }

    it 'does not render' do
      render_inline(component)
      expect(page).to have_no_css 'svg'
    end
  end
end
