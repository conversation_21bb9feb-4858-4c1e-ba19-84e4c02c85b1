require 'rails_helper'

RSpec.describe Brokerages::RequestForProposalComponent, type: :component do
  describe '#render?' do
    subject(:component) { described_class.new(brokerage:, user:) }

    let(:brokerage) { company.as_entity(:broker) }
    let(:user) { nil }

    context 'when the brokerage is active' do
      let(:company) { create :company }

      context 'when the brokerage profile is claimed' do
        before do
          create(:brokerage_profile, company:)
            .then { |profile| create :brokerage_profile_user, :verified, brokerage_profile: profile }
        end

        context 'when the brokerage has broker operating authority' do
          before do
            create :operating_authority, company:, broker_authority: 'active'
          end

          it 'returns true' do
            expect(component.render?).to be_truthy
          end

          context 'when user is a shipper' do
            let(:user) { create :user, :shipper }

            it 'renders link to form' do
              render_inline component
              expect(page).to have_link 'Request Quote'
            end
          end

          context 'when user is blank' do
            it 'renders link to form' do
              render_inline component
              expect(page).to have_link 'Request Quote'
            end
          end
        end

        context 'when the brokerage does not have broker operating authority' do
          it 'returns false' do
            expect(component.render?).to be_falsey
          end
        end
      end

      context 'when the brokerage profile is not claimed' do
        it 'returns false' do
          expect(component.render?).to be_falsey
        end
      end
    end

    context 'when the brokerage is not active' do
      let(:company) { create :company, dot_number: Census.maximum(:id).to_i.next }

      it 'returns false' do
        expect(component.render?).to be_falsey
      end
    end

    context 'when the company email is missing' do
      let(:company) { create :company, email: nil }

      it 'returns false' do
        expect(component.render?).to be_falsey
      end
    end
  end
end
