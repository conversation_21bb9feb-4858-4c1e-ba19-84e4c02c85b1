require 'rails_helper'

RSpec.describe Brokerages::OnboardingButtonComponent, type: :component do
  subject(:component) { described_class.new(brokerage:, onboarding:, mobile:) }

  let(:brokerage) { create(:company, :broker, legal_name: 'Cooler Logistics').as_entity(:broker) }
  let(:brokerage_profile) { create :brokerage_profile, company: brokerage.company }
  let(:mobile) { false }

  describe '#render?' do
    context 'when onboarding is present and persisted' do
      let(:onboarding) { create :brokerage_onboarding, brokerage_profile: }

      it 'returns true' do
        expect(component.render?).to be true
      end
    end

    context 'when onboarding is nil' do
      let(:onboarding) { nil }

      it 'returns false' do
        expect(component.render?).to be false
      end
    end
  end

  describe '#css_classes' do
    let(:onboarding) { build :brokerage_onboarding, brokerage_profile: }

    context 'when mobile is true' do
      let(:mobile) { true }

      it 'includes mobile classes' do
        expect(component.css_classes).to include('lg:hidden mt-6')
      end
    end

    context 'when mobile is false' do
      it 'includes desktop classes' do
        expect(component.css_classes).to include('hidden lg:flex')
      end
    end
  end

  describe 'rendering' do
    let(:onboarding) do
      create :brokerage_onboarding, brokerage_profile:,
                                    onboarding_system: 'Highway', onboarding_link: 'https://highway.example.com'
    end

    context 'when onboarding exists' do
      it 'renders the onboarding button' do
        render_inline(component)

        expect(page).to have_link(href: 'https://highway.example.com')
        expect(page).to have_content('Onboard')
        expect(page).to have_content("with #{brokerage.name}")
      end

      it 'opens link in new tab' do
        render_inline(component)
        expect(page).to have_link(href: 'https://highway.example.com', target: '_blank')
      end
    end

    context 'when onboarding does not exist' do
      let(:onboarding) { nil }

      it 'does not render anything' do
        render_inline(component)
        expect(page).to have_no_content('Onboard')
      end
    end
  end
end
