require 'rails_helper'

RSpec.describe Brokerages::UserContactComponent, type: :component do
  subject(:component) { described_class.new(brokerage:) }

  let(:brokerage_profile) { create :brokerage_profile }
  let(:brokerage) { brokerage_profile.brokerage }

  before do
    brokerage_profile.contacts.build(type: 'owner', name: '<PERSON>', email: '<PERSON>', phone: '8006001234')
  end

  it 'renders link to contact information' do
    render_inline component
    expect(page).to have_link('Contact', href: vc_test_controller.brokerage_contact_url(brokerage))
  end
end
