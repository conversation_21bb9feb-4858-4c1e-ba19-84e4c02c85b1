require 'rails_helper'

RSpec.describe Brokerages::WriteReviewListingComponent, type: :component do
  subject(:component) { described_class.new(brokerage:) }

  let(:company) { create :company, :broker, :with_city }
  let(:brokerage) { company.as_entity(:broker) }

  before do
    create :operating_authority, company:, docket_number: 'MC1072536', broker_authority: 'active'
  end

  it 'renders' do
    render_inline component
    expect(page).to have_text 'MC1072536'
  end
end
