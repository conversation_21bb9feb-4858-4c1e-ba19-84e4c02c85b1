require 'rails_helper'

RSpec.describe CompanyDashboard::ContactComponent, type: :component do
  subject(:component) { described_class.new(contact:) }

  context 'when contact is BrokerageProfileContact' do
    let(:contact) do
      create :brokerage_profile_contact, name: '<PERSON>', email: '<EMAIL>', phone: '8006001234'
    end

    it 'renders contact information' do
      render_inline(component)
      expect(page).to have_content '<PERSON>'
      expect(page).to have_content '<EMAIL>'
      expect(page).to have_content '8006001234'
    end
  end

  context 'when contact is CarrierProfileContact' do
    let(:contact) do
      create :carrier_profile_contact, name: '<PERSON>', email: '<EMAIL>', phone: '8006001234'
    end

    it 'renders contact information' do
      render_inline(component)
      expect(page).to have_content '<PERSON>'
      expect(page).to have_content '<EMAIL>'
      expect(page).to have_content '8006001234'
    end
  end
end
