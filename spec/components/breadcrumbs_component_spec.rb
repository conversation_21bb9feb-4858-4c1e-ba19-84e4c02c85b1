require 'rails_helper'

RSpec.describe BreadcrumbsComponent, type: :component do
  subject(:component) { described_class.new(breadcrumbs) }

  let(:breadcrumbs) { TruckingCompanies::Breadcrumbs::City.new(city).items }
  let(:city) { create :city, name: 'Chicago', state_code: 'IL' }

  it 'renders pipe separated breadcrumbs' do
    render_inline(component)
    expect(page.text.squish).to match 'Trucking Companies | United States | Illinois | Chicago'
  end
end
