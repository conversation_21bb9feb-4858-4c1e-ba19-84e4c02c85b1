require 'rails_helper'

RSpec.describe BrokerageReviewComponent, type: :component do
  subject(:component) { described_class.new(review:, featured:) }

  let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
  let(:carrier) { create :persona, :carrier, city: }
  let(:user) { create :user, first_name: '<PERSON>', last_name: '<PERSON>', carrier: }

  let!(:review) do
    create :brokerage_review, :carrier, :approved, user:, communication: 8, is_consider_next_time: true,
                                                   **review_attributes
  end

  let(:review_attributes) { {} }
  let(:featured) { false }

  describe 'featured review' do
    let(:featured) { true }
    let(:profile) { create :brokerage_profile }
    let(:review_attributes) { { featured: true, company: profile.company } }

    before do
      create :access_package, :brokerage, resource: profile
    end

    it 'renders featured elements' do
      render_inline(component)
      expect(page).to have_css 'svg.test-waves'
      expect(page).to have_text 'Featured Review'
    end
  end

  describe '#tagline' do
    context 'when review is anonymous' do
      let(:review_attributes) { { anonymous: true } }

      it 'disguises reviewer name' do
        render_inline(component)
        expect(page).to have_text 'Verified Carrier from Illinois'
      end
    end

    context 'when review is not anonymous' do
      it 'renders the reviewer name' do
        render_inline(component)
        expect(page).to have_text 'Jason B'
      end
    end
  end

  describe '#freights' do
    let(:review_attributes) { { freights: [freights(:genfreight).id, freights(:produce).id] } }

    it 'renders freight details' do
      render_inline(component)
      expect(page).to have_text 'General Freight'
      expect(page).to have_text 'Fresh Produce'
    end
  end

  describe '#positive_sentiments' do
    before do
      create(:sentiment, :positive, label: 'Friendly')
        .then { |sentiment| review.review_sentiments.create(sentiment:) }
    end

    it 'renders positive sentiments' do
      render_inline component
      expect(page).to have_text 'Friendly'
    end
  end

  describe '#negative_sentiments' do
    before do
      create(:sentiment, :negative, label: 'Fraud')
        .then { |sentiment| review.review_sentiments.create(sentiment:) }
    end

    it 'renders negative sentiments' do
      render_inline component
      expect(page).to have_text 'Fraud'
    end
  end
end
