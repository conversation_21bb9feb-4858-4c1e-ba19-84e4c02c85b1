class TableComponentPreview < ViewComponent::Preview
  def default
    render TableComponent.new(
      table_values: [
        ['Vehicle maintenance', 960, 149, 285, 3.05, '29.1%'],
        ['Unsafe driving', 362, 90, 1, 1.09, '14.8%'],
        ['Hours of service compliance', 162, 27, 11, 0.15, '8.2%'],
        ['Driver fitness', 13, 8, 11, 0.04, '4.3%'],
        ['Controlled substances', 2, 0, 2, 0, '0.0%']
      ], caption: 'Safety Violations'
    ) do |c|
      c.with_header('Type', class: 'text-left sr-only')
      c.with_header('Total', class: 'text-center px-3 lg:px-7')
      c.with_header('Severe', class: 'text-center px-3 lg:px-7')
      c.with_header('Out of Service', class: 'text-center px-3 lg:px-7')
      c.with_header('Measure', class: 'text-center px-3 lg:px-7')
      c.with_header('Percentile', class: 'text-center px-3 lg:px-7')
    end
  end
end
