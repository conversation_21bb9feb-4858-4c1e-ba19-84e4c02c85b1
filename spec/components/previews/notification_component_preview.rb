class NotificationComponentPreview < ViewComponent::Preview
  # @param delay [Integer]
  def alert(delay: 3000)
    render NotificationComponent.new(type: :alert, delay:) do |c|
      c.with_title { 'Uh oh, something went wrong' }
      c.with_description { 'Please try again later' }
    end
  end

  # @param delay [Integer]
  def notice(delay: 3000)
    render NotificationComponent.new(type: :notice, delay:) do |c|
      c.with_title { 'Saved Successfully!' }
      c.with_description { 'Your changes should be live shortly' }
    end
  end
end
