class SponsoredContentComponentPreview < ViewComponent::Preview
  # @param orientation [Symbol] select [horizontal, vertical]
  def default(orientation: :horizontal)
    carrier = Carrier.indexable.joins(profile: :access_package).order(Arel.sql('RANDOM()')).first

    render SponsoredContentComponent.new(
      SponsoredContents::Resource.carrier(carrier.company), type: :default, orientation:
    )
  end

  # @param orientation [Symbol] select [horizontal, vertical]
  def review(orientation: :horizontal)
    carrier =
      Carrier.joins(:reviews)
        .merge(Review.approved.joins(:sentiments).where(featured: true))
        .order(Arel.sql('RANDOM()'))
        .first

    render SponsoredContentComponent.new(
      SponsoredContents::Resource.carrier(carrier.company), type: :review, orientation:
    )
  end
end
