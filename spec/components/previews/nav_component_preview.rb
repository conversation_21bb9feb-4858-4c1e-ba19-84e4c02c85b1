class NavComponentPreview < ViewComponent::Preview
  def default
    render(Nav::ContainerComponent.new) do |c|
      c.with_item(name: 'Overview')

      c.with_item(name: 'Profile', active: true) do |i|
        i.with_child(name: 'Carrier Page')
        i.with_child(name: 'Contact Info', active: true)
        i.with_child(name: 'Subscription')
        i.with_child(name: 'Account')
      end

      c.with_item(name: 'Reviews')
      c.with_item(name: 'Media & Downloads')
      c.with_item(name: 'Analytics')
      c.with_item(name: 'Widgets')
      c.with_item(name: 'References')
    end
  end
end
