require 'rails_helper'

RSpec.describe ProfileSponsoredContentComponent, type: :component do
  let(:component) { described_class.new(resource) }

  context 'with carrier provider' do
    let(:carrier_profile) { create :carrier_profile }
    let(:company) { carrier_profile.company }
    let(:resource) { SponsoredContents::Resource.carrier(company) }

    before do
      create :review, :approved, company:, featured: true
    end

    it 'renders' do
      render_inline component
      expect(page).to have_text company.name
    end
  end

  context 'with brokerage provider' do
    let(:brokerage_profile) { create :brokerage_profile }
    let(:company) { brokerage_profile.company }
    let(:resource) { SponsoredContents::Resource.brokerage(company) }

    before do
      create :brokerage_review, :carrier, :approved, company:, featured: true
    end

    it 'renders' do
      render_inline component
      expect(page).to have_text company.name
    end
  end
end
