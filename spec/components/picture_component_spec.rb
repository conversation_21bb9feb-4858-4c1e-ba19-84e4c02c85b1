require 'rails_helper'

RSpec.describe PictureComponent, type: :component do
  subject(:component) { described_class.new(attachment.logo, dimensions:) }

  let(:attachment) { create :carrier_profile, logo: fixture_file_upload(*logo) }
  let(:dimensions) { [64, 64] }

  context 'when logo is png' do
    let(:logo) { %w(logo.png image/png) }

    it 'renders' do
      render_inline component
      expect(page).to have_css 'picture'
    end

    context 'when a dimension is missing' do
      let(:dimensions) { [64, nil] }

      it 'does not raise error' do
        render_inline component
        expect(page).to have_css 'picture'
      end
    end
  end

  context 'when logo is svg' do
    let(:logo) { %w(logo.svg image/svg+xml) }

    it 'renders' do
      render_inline component
      expect(page).to have_no_css 'picture'
      expect(page).to have_css 'svg'
    end
  end
end
