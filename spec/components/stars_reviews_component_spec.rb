require 'rails_helper'

RSpec.describe StarsReviewsComponent, type: :component do
  subject(:component) { described_class.new(**attributes) }

  context 'when link is false' do
    let(:attributes) { { link: false } }

    it 'does not render link' do
      render_inline(component)
      expect(page).to have_no_css 'a'
    end
  end

  context 'when link is present' do
    let(:attributes) { { link: '/carriers/fr8-dog' } }

    it 'renders link' do
      render_inline(component)
      expect(page).to have_xpath '//a[@href="/carriers/fr8-dog"]'
    end
  end
end
