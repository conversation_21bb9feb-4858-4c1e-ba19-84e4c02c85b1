require 'rails_helper'

RSpec.describe SponsoredContentComponent, type: :component do
  shared_examples 'a carrier rendering component' do |type, orientation|
    let(:component) { described_class.new(resource, type:, orientation:) }
    let(:carrier_profile) { create :carrier_profile }
    let(:company) { carrier_profile.company }
    let(:resource) { SponsoredContents::Resource.carrier(company) }

    it 'renders' do
      expect { render_inline(component) }.not_to raise_error
    end
  end

  shared_examples 'a brokerage rendering component' do |type, orientation|
    let(:component) { described_class.new(resource, type:, orientation:) }
    let(:brokerage_profile) { create :brokerage_profile }
    let(:company) { brokerage_profile.company }
    let(:resource) { SponsoredContents::Resource.brokerage(company) }

    it 'renders' do
      expect { render_inline(component) }.not_to raise_error
    end
  end

  it_behaves_like 'a carrier rendering component', :default, :horizontal
  it_behaves_like 'a carrier rendering component', :default, :vertical

  it_behaves_like 'a carrier rendering component', :review, :horizontal do
    before do
      create :review, :approved, company:, featured: true
    end
  end

  it_behaves_like 'a carrier rendering component', :review, :vertical do
    before do
      create :review, :approved, company:, featured: true
    end
  end

  it_behaves_like 'a brokerage rendering component', :default, :horizontal
  it_behaves_like 'a brokerage rendering component', :default, :vertical

  it_behaves_like 'a brokerage rendering component', :review, :horizontal do
    before do
      create :brokerage_review, :carrier, :approved, company:, featured: true
    end
  end

  it_behaves_like 'a brokerage rendering component', :review, :vertical do
    before do
      create :brokerage_review, :carrier, :approved, company:, featured: true
    end
  end

  context 'when type is review' do
    subject(:component) { described_class.new(resource, type: :review) }

    let(:carrier_profile) { create :carrier_profile }
    let(:company) { carrier_profile.company }
    let(:resource) { SponsoredContents::Resource.carrier(company) }

    context 'when carrier has featured review' do
      before do
        create :review, :approved, company:, featured: true
      end

      it 'returns review component' do
        expect(component.component).to be_a ReviewSponsoredContentComponent
      end
    end

    context 'when carrier does not have featured review' do
      before do
        create :review, :approved, company:, featured: false
      end

      it 'returns default component' do
        expect(component.component).to be_a DefaultSponsoredContentComponent
      end
    end
  end

  context 'when type is default' do
    subject(:component) { described_class.new(resource) }

    let(:carrier_profile) { create :carrier_profile }
    let(:company) { carrier_profile.company }
    let(:resource) { SponsoredContents::Resource.carrier(company) }

    it 'returns default component' do
      expect(component.component).to be_a DefaultSponsoredContentComponent
    end
  end
end
