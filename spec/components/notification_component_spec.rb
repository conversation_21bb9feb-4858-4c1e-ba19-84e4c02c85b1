require 'rails_helper'

RSpec.describe NotificationComponent, type: :component do
  subject(:component) { described_class.new(type:) }

  context 'with alert type' do
    let(:type) { :alert }

    it 'renders' do
      render_inline component
      expect(page).to have_css '.border-t-secondary'
    end
  end

  context 'with notice type' do
    let(:type) { :notice }

    it 'renders' do
      render_inline component
      expect(page).to have_css '.border-t-green'
    end
  end
end
