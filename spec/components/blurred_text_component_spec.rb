require 'rails_helper'

RSpec.describe BlurredTextComponent, type: :component do
  let(:component) { described_class.new(text: 'Hello, World!', blur:) }

  context 'when text is blurred' do
    let(:blur) { true }

    it 'renders text with blur' do
      render_inline component
      expect(page).to have_css('span.blur-sm', text: '***** ******')
    end
  end

  context 'when text is not blurred' do
    let(:blur) { false }

    it 'renders text without blur' do
      render_inline component
      expect(page).to have_no_css('span.blur-sm')
      expect(page).to have_css('span', text: 'Hello, World!')
    end
  end
end
