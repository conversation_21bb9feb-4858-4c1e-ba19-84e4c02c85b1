require 'rails_helper'

RSpec.describe RequestForProposalComponent, type: :component do
  describe '#render?' do
    subject(:component) { described_class.new(carrier:, user: nil) }

    let(:carrier_profile) { create :carrier_profile, company: }
    let(:company) { create :company, :carrier }
    let(:carrier) { carrier_profile.carrier }

    context 'when the carrier is active' do
      context 'when the carrier profile is claimed' do
        before do
          create :carrier_profile_user, :verified, carrier_profile:
        end

        context 'when the carrier has operating authorities' do
          let(:company) { create :company, :with_authority, carrier_operation: }

          context 'with interstate operation' do
            let(:carrier_operation) { 'interstate' }

            it 'returns true' do
              expect(component.render?).to be_truthy
            end
          end

          context 'with intrastate operation' do
            let(:carrier_operation) { 'intrastate' }

            it 'returns true' do
              expect(component.render?).to be_truthy
            end
          end
        end

        context 'when the carrier does not have operating authorities' do
          let(:company) { create :company, carrier_operation: }

          context 'with interstate operation' do
            let(:carrier_operation) { 'interstate' }

            it 'returns false' do
              expect(component.render?).to be_falsey
            end
          end

          context 'with intrastate operation' do
            let(:carrier_operation) { 'intrastate' }

            it 'returns true' do
              expect(component.render?).to be_truthy
            end
          end
        end
      end

      context 'when the carrier profile is not claimed' do
        let(:company) { create :company, :with_authority, carrier_operation: 'intrastate' }

        it 'returns false' do
          expect(component.render?).to be_falsey
        end
      end
    end

    context 'when the carrier is not active' do
      let(:company) { create :company, dot_number: Census.maximum(:id).to_i.next }

      it 'returns false' do
        expect(component.render?).to be_falsey
      end
    end
  end
end
