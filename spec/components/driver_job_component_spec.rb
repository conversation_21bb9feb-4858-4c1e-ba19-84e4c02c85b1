require 'rails_helper'

RSpec.describe DriverJobComponent, type: :component do
  subject(:component) { described_class.new(driver_job:) }

  let(:carrier_profile) { create :carrier_profile }

  let(:driver_job) do
    create :driver_job, company: carrier_profile.company, salary_min: 0.53, salary_max: 0.62, salary_unit: 'mile'
  end

  it 'renders the component' do
    render_inline component
    expect(page).to have_text '$0.53 - $0.62 per mile'
  end

  context 'with external driver application url' do
    before do
      carrier_profile.update(driver_application_url: 'www.example.com')
    end

    it 'renders the component' do
      render_inline component
      expect(page).to have_link 'Apply Now', href: 'http://www.example.com'
    end
  end
end
