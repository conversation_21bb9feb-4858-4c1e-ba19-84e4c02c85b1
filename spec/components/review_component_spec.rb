require 'rails_helper'

RSpec.describe ReviewComponent, type: :component do
  subject(:component) { described_class.new(review:, featured:) }

  let(:city) { create :city, name: 'Chicago', state_code: 'IL' }
  let(:broker) { create :persona, :broker, city: }
  let(:user) { create :user, first_name: '<PERSON>', last_name: '<PERSON>', broker: }

  let!(:review) do
    create :review, :approved, user:, timeliness: 10, cleanliness: 9, communication: 8, is_consider_next_time: true,
                               **review_attributes
  end

  let(:review_attributes) { {} }

  let(:featured) { false }

  describe 'featured review' do
    let(:featured) { true }
    let(:profile) { create :carrier_profile }
    let(:review_attributes) { { featured: true, company: profile.company } }

    before do
      create :access_package, :carrier, resource: profile
    end

    it 'renders featured elements' do
      render_inline(component)
      expect(page).to have_css 'svg.test-waves'
      expect(page).to have_text 'Featured Review'
    end
  end

  describe '#tagline' do
    context 'when review is anonymous' do
      let(:review_attributes) { { anonymous: true } }

      it 'disguises reviewer name' do
        render_inline(component)
        expect(page).to have_text 'Verified Broker from Illinois'
      end
    end

    context 'when review is not anonymous' do
      it 'renders the reviewer name' do
        render_inline(component)
        expect(page).to have_text 'Jason B'
      end
    end
  end

  describe '#freights' do
    let(:review_attributes) { { freights: [freights(:genfreight).id, freights(:produce).id] } }

    it 'renders freight details' do
      render_inline(component)
      expect(page).to have_text 'General Freight'
      expect(page).to have_text 'Fresh Produce'
    end
  end

  describe '#tracking_functions' do
    context 'when not specified' do
      it 'does not render tracking section' do
        render_inline(component)
        expect(page).to have_no_text 'Tracking'
      end
    end

    context 'when tracking is offered' do
      let(:review_attributes) { { offer_electronic_tracking: true } }

      it 'renders correct text' do
        render_inline(component)
        expect(page).to have_text 'Offers electronic tracking'
      end
    end

    context 'when tracking is not offered' do
      let(:review_attributes) { { offer_electronic_tracking: false } }

      it 'renders correct text' do
        render_inline(component)
        expect(page).to have_text 'Did not use electronic tracking'
      end
    end

    context 'when tracking worked' do
      let(:review_attributes) { { electronic_tracking_worked: true } }

      it 'renders correct text' do
        render_inline(component)
        expect(page).to have_text 'Tracking functional'
      end
    end

    context 'when tracking did not work' do
      let(:review_attributes) { { electronic_tracking_worked: false } }

      it 'renders correct text' do
        render_inline(component)
        expect(page).to have_text 'Tracking not functional'
      end
    end
  end

  describe '#positive_sentiments' do
    before do
      create(:sentiment, :positive, label: 'Friendly')
        .then { |sentiment| review.review_sentiments.create(sentiment:) }
    end

    it 'renders positive sentiments' do
      render_inline component
      expect(page).to have_text 'Friendly'
    end
  end

  describe '#negative_sentiments' do
    before do
      create(:sentiment, :negative, label: 'Fraud')
        .then { |sentiment| review.review_sentiments.create(sentiment:) }
    end

    it 'renders negative sentiments' do
      render_inline component
      expect(page).to have_text 'Fraud'
    end
  end
end
