require 'rails_helper'

RSpec.describe CompanyNoteComponent, type: :component do
  subject(:component) { described_class.new(entity:, active:) }

  before do
    vc_test_controller.current_user = build_stubbed(:user, :broker)
  end

  context 'when entity is a carrier' do
    let(:company) { create :company, :carrier }
    let(:entity) { company.as_entity(:carrier) }

    context 'when active' do
      let(:active) { true }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Edit Notes'
      end
    end

    context 'when not active' do
      let(:active) { false }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Add Notes'
      end
    end
  end

  context 'when entity is a brokerage' do
    let(:company) { create :company, :broker }
    let(:entity) { company.as_entity(:broker) }

    context 'when active' do
      let(:active) { true }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Edit Notes'
      end
    end

    context 'when not active' do
      let(:active) { false }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Add Notes'
      end
    end
  end
end
