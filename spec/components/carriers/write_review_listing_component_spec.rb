require 'rails_helper'

RSpec.describe Carriers::WriteReviewListingComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:company) { create :company, :with_city }
  let(:carrier) { company.as_entity(:carrier) }

  before do
    create :operating_authority, company:, docket_number: 'MC1072536'
  end

  it 'renders' do
    render_inline component
    expect(page).to have_text 'MC1072536'
  end
end
