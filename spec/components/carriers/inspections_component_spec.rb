require 'rails_helper'

RSpec.describe Carriers::InspectionsComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:carrier) { instance_double Carrier, inspection: }

  context 'when inspection exists' do
    let(:inspection) do
      instance_double Inspection, inspections: 7, driver_insp: 5, driver_oos: 4, vehicle_insp: 2, vehicle_oos: 1
    end

    it 'renders table for inspection' do
      render_inline(component)
      expect(page.text.squish).to match 'Driver 5 4 80% 5.82%'
      expect(page.text.squish).to match 'Vehicle 2 1 50% 21.19%'
    end
  end

  context 'when inspection does not exist' do
    let(:inspection) { nil }

    it 'renders table with zeros' do
      render_inline(component)
      expect(page.text.squish).to match 'Driver 0 0 0% 5.82%'
      expect(page.text.squish).to match 'Vehicle 0 0 0% 21.19%'
    end
  end
end
