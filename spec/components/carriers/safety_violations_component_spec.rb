require 'rails_helper'

RSpec.describe Carriers::SafetyViolationsComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:carrier) { instance_double Carrier, safety_violations: }

  let(:safety_violations) do
    [
      instance_double(CarriersSafetyViolation, basic_desc: 'HOURS_OF_SERVICE_COMPLIANCE', regular: 5, severe: 1, oos: 0,
                                               total: 6, measure: 0.11, percentile: 5.4957),
      instance_double(CarriersSafetyViolation, basic_desc: 'UNSAFE_DRIVING', regular: 25, severe: 7, oos: 0, total: 32,
                                               measure: 1.95, percentile: 26.7566),
      instance_double(CarriersSafetyViolation, basic_desc: 'VEHICLE_MAINTENANCE', regular: 27, severe: 8, oos: 18,
                                               total: 45, measure: 3.99, percentile: 35.8741)
    ]
  end

  it 'renders violations table' do
    render_inline(component)
    page.text.squish.tap do |text|
      expect(text).to match 'Vehicle Maintenance 45 8 18 3.99 35.9%'
      expect(text).to match 'Unsafe Driving 32 7 0 1.95 26.8%'
      expect(text).to match 'Hours of Service Compliance 6 1 0 0.11 5.5%'
      expect(text).to match 'Driver Fitness 0 0 0 0 0%'
      expect(text).to match 'Controlled Substances 0 0 0 0 0%'
    end
  end
end
