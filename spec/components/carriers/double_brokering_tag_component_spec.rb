require 'rails_helper'

RSpec.describe Carriers::DoubleBrokeringTagComponent, type: :component do
  subject(:component) { described_class.new(carrier) }

  let(:company) { create :company, :carrier }
  let(:carrier) { company.as_entity(:carrier) }
  let(:sentiment) { create :sentiment, :negative, label: 'Double Brokering' }

  context 'with double brokering sentiment' do
    before do
      create(:review, :approved, company:).then do |review|
        review.review_sentiments.create(sentiment:)
      end

      Companies::UpsertReviewAggregate.call(carrier)
    end

    it 'renders the component' do
      render_inline(component)
      expect(page).to have_content 'Double Brokering'
    end
  end

  context 'without double brokering sentiment' do
    it 'does not render the component' do
      render_inline(component)
      expect(page).to have_no_content 'Double Brokering'
    end
  end
end
