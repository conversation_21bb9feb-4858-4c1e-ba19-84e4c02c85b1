require 'rails_helper'

RSpec.describe Carriers::AccidentReportComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:carrier) { instance_double Carrier, accidents: }

  let(:accidents) do
    [
      instance_double(CarriersAccident, bucket: 0...6, crashes: 3, fatalities: 0, injuries: 2, tow_aways: 3),
      instance_double(CarriersAccident, bucket: 6...12, crashes: 5, fatalities: 1, injuries: 0, tow_aways: 4),
      instance_double(CarriersAccident, bucket: 24.., crashes: 1, fatalities: 1, injuries: 3, tow_aways: 5)
    ]
  end

  it 'renders accident summary table' do
    render_inline(component)

    page.text.squish.tap do |text|
      expect(text).to match 'Fatalities 0 1 0 1'
      expect(text).to match 'Injuries 2 0 0 2'
      expect(text).to match 'Tow-away 3 4 0 7'
      expect(text).to match 'Crash Reports 3 5 0 8'
    end
  end
end
