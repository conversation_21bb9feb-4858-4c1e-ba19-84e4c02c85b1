require 'rails_helper'

RSpec.describe Carriers::FleetDetailsComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:carrier) { instance_double Carrier, census:, legacy_census: nil }

  context 'when census is present' do
    let(:census) do
      build :census, owntract: '2', owntrail: '0', owntruck: '3', trmtract: '3', trmtrail: '4', trmtruck: '1',
                     trptract: '0', trptrail: '5', trptruck: '6'
    end

    it 'renders fleet details table' do
      render_inline(component)

      page.text.squish.tap do |text|
        expect(text).to match 'Owned 2 0 3'
        expect(text).to match 'Leased 3 9 7'
      end
    end
  end

  context 'when census is not present' do
    let(:census) { nil }

    it 'renders all zeros' do
      render_inline(component)

      page.text.squish.tap do |text|
        expect(text).to match 'Owned 0 0 0'
        expect(text).to match 'Leased 0 0 0'
      end
    end
  end
end
