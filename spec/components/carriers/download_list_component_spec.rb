require 'rails_helper'

RSpec.describe Carriers::DownloadListComponent, type: :component do
  subject(:component) { described_class.new(user:) }

  context 'when user is missing' do
    let(:user) { nil }

    it 'renders' do
      expect { render_inline(component) }.not_to raise_error
    end
  end

  context 'when user is present' do
    let(:user) { create :user }

    context 'when access is missing' do
      context 'when user is broker' do
        let(:user) { create :user, :broker }

        it 'renders' do
          expect { render_inline(component) }.not_to raise_error
        end
      end

      context 'when user is carrier' do
        let(:user) { create :user, :carrier }

        it 'renders' do
          expect { render_inline(component) }.not_to raise_error
        end
      end
    end

    context 'when access is granted' do
      before do
        create :access_package, :broker, resource: user
      end

      it 'renders' do
        expect { render_inline(component) }.not_to raise_error
      end
    end
  end
end
