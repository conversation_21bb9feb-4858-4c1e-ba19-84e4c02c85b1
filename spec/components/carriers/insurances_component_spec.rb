require 'rails_helper'

RSpec.describe Carriers::InsurancesComponent, type: :component do
  subject(:component) { described_class.new(insurances:) }

  context 'when there are no insurance records' do
    let(:insurances) { [] }

    it 'does not render' do
      expect(component.render?).to be false
    end
  end

  context 'when there are insurance records' do
    let(:insurances) do
      [
        build(:insurance, :bipd, insurance_company_name: 'United States Fire Insurance Co.', bi_pd_max_limit: 500),
        build(:insurance, :cargo, insurance_company_name: 'National Continental Insurance Co.', bi_pd_max_limit: 750),
        build(:insurance, :bond, insurance_company_name: 'Pacific Financial Association, Inc.', bi_pd_max_limit: 0)
      ]
    end

    it 'renders insurance coverage for cargo' do
      render_inline(component)

      page.text.squish.tap do |text|
        expect(text).to include 'Insurance Type Cargo'
        expect(text).to include 'Company Name National Continental Insurance Co.'
        expect(text).to include 'Policy Limit $750,000'
      end
    end

    it 'renders insurance coverage for BIPD' do
      render_inline(component)

      page.text.squish.tap do |text|
        expect(text).to include 'Insurance Type BIPD'
        expect(text).to include 'Company Name United States Fire Insurance Co.'
        expect(text).to include 'Policy Limit $500,000'
      end
    end

    it 'renders bond coverage for BOND' do
      render_inline(component)

      page.text.squish.tap do |text|
        expect(text).to include 'Bond Type Surety Bond'
        expect(text).to include 'Pacific Financial Association, Inc.'
      end
    end
  end
end
