require 'rails_helper'

RSpec.describe Carriers::OperationTagComponent, type: :component do
  subject(:component) { described_class.new(carrier) }

  let(:carrier) { build_stubbed :company, carrier_operation: }

  context 'when carrier_operation is interstate' do
    let(:carrier_operation) { 'interstate' }

    it 'renders' do
      render_inline(component)
      expect(page).to have_text 'Interstate'
    end
  end

  context 'when carrier_operation is intrastate' do
    let(:carrier_operation) { 'intrastate' }

    it 'renders' do
      render_inline(component)
      expect(page).to have_text 'Intrastate'
    end
  end
end
