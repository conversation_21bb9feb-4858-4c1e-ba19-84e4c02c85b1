require 'rails_helper'

RSpec.describe HorizontalDefaultSponsoredContentComponent, type: :component do
  subject(:component) { described_class.new(resource, provider) }

  let(:resource) { SponsoredContents::Resource.carrier(profile.company) }
  let(:profile) { create :carrier_profile }
  let(:provider) { SponsoredContents::EntityProviders.for('carrier').new(profile.company) }

  it 'renders' do
    render_inline(component)
    expect(page).to have_text 'Learn More'
  end
end
