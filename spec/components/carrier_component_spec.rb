require 'rails_helper'

RSpec.describe CarrierComponent, type: :component do
  subject(:component) { described_class.new(carrier:) }

  let(:company) { create :company, :with_city, :with_authority, **carrier_attributes }
  let(:carrier) { company.as_entity(:carrier) }

  let(:carrier_attributes) { {} }

  describe 'fleet size' do
    let(:carrier_attributes) { { power_units: 76_492 } }

    it 'renders comma-delimited fleet size' do
      render_inline(component)
      expect(page).to have_text '76,492'
    end
  end

  describe 'authority' do
    let(:docket_number) { 'MC378653' }
    let(:carrier_attributes) { { common_authority: 'active', docket_number: } }

    before do
      Authhist.create(docket_number:, common: Date.new(2015, 6, 17))
    end

    it 'renders authority with date' do
      render_inline(component)
      expect(page).to have_text 'Common Active'
      expect(page).to have_text '(06/17/2015)'
    end
  end

  describe 'insurance' do
    before do
      create :insurance, :bipd, operating_authority: carrier.operating_authorities.first, bi_pd_max_limit: 750
    end

    it 'renders correct currency' do
      render_inline(component)
      expect(page).to have_text '$750,000'
    end
  end

  describe 'safety rating' do
    let(:carrier_attributes) { { safety_rating: 'satisfactory' } }

    it 'renders correct rating' do
      render_inline(component)
      expect(page).to have_text 'Satisfactory'
    end
  end

  describe 'reviews' do
    before do
      create :review, :approved, company:, timeliness: 10, cleanliness: 7, communication: 8, is_consider_next_time: true
      Companies::UpsertReviewAggregate.call(carrier)
    end

    it 'renders review count' do
      render_inline(component)
      expect(page.text.squish).to match '1 review'
    end

    it 'renders star rating' do
      render_inline(component)
      expect(page.text.squish).to match '4.4 out of 5'
    end
  end
end
