require 'rails_helper'

RSpec.describe BookmarkComponent, type: :component do
  subject(:component) { described_class.new(entity:, active:) }

  let(:active) { false }

  before do
    vc_test_controller.current_user = build_stubbed(:user, :broker)
  end

  context 'when entity is carrier' do
    let(:company) { create :company, :carrier }
    let(:entity) { company.as_entity(:carrier) }

    context 'when active' do
      let(:active) { true }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Saved Carrier'
      end
    end

    context 'when not active' do
      let(:active) { false }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Save Carrier'
      end
    end
  end

  context 'when entity is broker' do
    let(:company) { create :company, :broker }
    let(:entity) { company.as_entity(:broker) }

    context 'when active' do
      let(:active) { true }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Saved Brokerage'
      end
    end

    context 'when not active' do
      let(:active) { false }

      it 'renders correct attributes' do
        render_inline component
        expect(page).to have_text 'Save Brokerage'
      end
    end
  end
end
