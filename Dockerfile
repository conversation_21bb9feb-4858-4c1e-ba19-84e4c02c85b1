# syntax = docker/dockerfile:1

# Make sure RUBY_VERSION matches the Ruby version in .tool-versions and Gemfile
ARG RUBY_VERSION=3.4.5
FROM registry.docker.com/library/ruby:$RUBY_VERSION-slim AS base

# Rails app lives here
WORKDIR /rails

ARG RAILS_ENV="production"

# Set production environment
ENV BUNDLE_DEPLOYMENT="1" \
    BUNDLE_PATH="/usr/local/bundle" \
    BUNDLE_WITHOUT="development:test"

# Update gems and bundler
RUN gem update --system --no-document && \
    gem install -N bundler

# Throw-away build stage to reduce size of final image
FROM base AS build
ARG BUNDLE_ENTERPRISE__CONTRIBSYS__COM

RUN echo "deb http://ftp.us.debian.org/debian sid main" > /etc/apt/sources.list.d/sid.list

# Install packages needed to build gems and node modules
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y build-essential curl git libvips42t64 pkg-config libpq-dev libyaml-dev libffi-dev node-gyp python-is-python3

# Install JavaScript dependencies
ARG NODE_ENV="production"
ARG NODE_VERSION=22.11.0
ARG YARN_VERSION=1.22.21
ENV PATH=/usr/local/node/bin:$PATH
RUN curl -sL https://github.com/nodenv/node-build/archive/master.tar.gz | tar xz -C /tmp/ && \
    /tmp/node-build-master/bin/node-build "${NODE_VERSION}" /usr/local/node && \
    npm install -g yarn@$YARN_VERSION && \
    rm -rf /tmp/node-build-master

# Install application gems
COPY --link Gemfile Gemfile.lock .tool-versions ./
RUN bundle install && \
    rm -rf ~/.bundle/ "${BUNDLE_PATH}"/ruby/*/cache "${BUNDLE_PATH}"/ruby/*/bundler/gems/*/.git && \
    bundle exec bootsnap precompile --gemfile

# Install node modules
COPY --link .yarn ./.yarn
COPY --link .yarnrc.yml ./
COPY --link package.json yarn.lock ./
RUN yarn install --immutable

# Copy application code
COPY --link . .

# Precompile bootsnap code for faster boot times
RUN bundle exec bootsnap precompile app/ lib/

# Precompiling assets for production
RUN yarn build
RUN RAILS_SKIP_MASTER_KEY=true SECRET_KEY_BASE_DUMMY=1 ./bin/rails assets:precompile

# Remove development dependencies
RUN yarn workspaces focus --all --production && yarn cache clean

# Final stage for app image
FROM base

RUN echo "deb http://ftp.us.debian.org/debian sid main" > /etc/apt/sources.list.d/sid.list

# Install packages needed for deployment
RUN apt-get update -qq && \
    apt-get install --no-install-recommends -y curl jq libjemalloc2 libpq-dev libvips42t64 libvips-tools mupdf-tools && \
    rm -rf /var/lib/apt/lists /var/cache/apt/archives

# Copy built artifacts: gems, application
COPY --from=build /usr/local/bundle /usr/local/bundle
COPY --from=build /usr/local/node /usr/local/node
COPY --from=build /rails /rails

# Run and own only the runtime files as a non-root user for security
RUN useradd rails --create-home --shell /bin/bash && \
    chown -R rails:rails db log storage tmp
USER rails:rails

# Deployment options
ENV LD_PRELOAD="libjemalloc.so.2" \
    MALLOC_CONF="dirty_decay_ms:1000,narenas:2,background_thread:true" \
    RUBY_YJIT_ENABLE="1" \
    PATH=/usr/local/node/bin:$PATH

# Entrypoint prepares the database.
ENTRYPOINT ["/rails/bin/docker-entrypoint"]

# Start the server by default, this can be overwritten at runtime
EXPOSE 3000
CMD ["./bin/rails", "server"]
